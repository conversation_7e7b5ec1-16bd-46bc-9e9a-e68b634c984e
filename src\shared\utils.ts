import sanitize from "sanitize-html";
import type { LLMService } from "./models";

export function sanitizeAndTrim(
  input?: string | null,
  options: sanitize.IOptions = {
    allowedTags: [],
    allowedAttributes: {},
  }
) {
  return sanitize((input ?? "").trim(), options);
}

export function redactIp(ip: string) {
  const ipv6 = ip.includes(":");
  return ipv6 ? "redacted:ipv6" : ip.replace(/\.\d+\.\d+$/, ".xxx.xxx");
}

export function assertNever(x: never): never {
  throw new Error(`Called assertNever with argument ${x}.`);
}

export const ObjectTyped = {
  keys: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.keys(obj)) as readonly [keyof TObject];
  },
  entries: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.entries(obj)) as readonly [keyof TObject, TObject[keyof TObject]][];
  },
  values: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.values(obj)) as readonly TObject[keyof TObject][];
  },
};

export function normalizeForDiff(value: unknown): any {
  if (value instanceof Map) {
    return Object.fromEntries(
      Array.from(value.entries()).map(([k, v]) => [k, normalizeForDiff(v)])
    );
  }

  if (value instanceof Set) {
    return Array.from(value.values()).map(normalizeForDiff);
  }

  if (Array.isArray(value)) {
    return value.map(normalizeForDiff);
  }

  if (value !== null && typeof value === "object") {
    return Object.fromEntries(Object.entries(value).map(([k, v]) => [k, normalizeForDiff(v)]));
  }

  return value;
}

export const numberFormat = new Intl.NumberFormat("en-US", {
  style: "decimal",
  maximumFractionDigits: 2,
});

/**
 * A tuple representing the result of an operation that can fail.
 * On success, it's `[data, null]`.
 * On failure, it's `[null, error]`.
 * @template T The type of the data on success.
 * @template E The specific type of the Error on failure.
 */
export type GoResult<T, E extends Error> = [T, null] | [null, E | Error];

/**
 * A tuple representing the asynchronous result of an operation that can fail.
 * This is a Promise that resolves to a GoResult.
 * @template T The type of the data on success.
 * @template E The specific type of the Error on failure.
 */
export type GoResultAsync<T, E extends Error> = Promise<GoResult<T, E>>;

/**
 * Wraps a synchronous function in a try-catch block and returns a GoResult tuple.
 *
 * @template T The return type of the function `fn`.
 * @template E A custom error class that extends Error.
 * @param {() => T} fn The synchronous function to execute.
 * @param {new (...args: any[]) => E} [errorClass] An optional custom error class to check for.
 * @returns {GoResult<T, E>} A tuple of `[data, error]`.
 */
export function tryCatchSync<T, E extends Error>(
  fn: () => T,
  errorClass?: new (...args: any[]) => E
): GoResult<T, E> {
  try {
    const data = fn();
    return [data, null];
  } catch (error) {
    if (errorClass && error instanceof errorClass) {
      return [null, error];
    }
    if (error instanceof Error) {
      return [null, error];
    }
    // If the thrown value is not an Error instance, wrap it in a new Error.
    return [null, new Error(String(error))];
  }
}

/**
 * Wraps an asynchronous function (that returns a Promise) in a try-catch block
 * and returns a Promise that resolves to a GoResult tuple.
 *
 * @template T The resolved type of the Promise returned by `promiseFn`.
 * @template E A custom error class that extends Error.
 * @param {() => Promise<T>} promiseFn The asynchronous function to execute.
 * @param {new (...args: any[]) => E} [errorClass] An optional custom error class to check for.
 * @returns {GoResultAsync<T, E>} A Promise resolving to a tuple of `[data, error]`.
 */
export async function tryCatch<T, E extends Error>(
  promiseFn: () => Promise<T>,
  errorClass?: new (...args: any[]) => E
): GoResultAsync<T, E> {
  try {
    const data = await promiseFn();
    return [data, null];
  } catch (error) {
    if (errorClass && error instanceof errorClass) {
      return [null, error];
    }
    if (error instanceof Error) {
      return [null, error];
    }
    // If the thrown value is not an Error instance, wrap it in a new Error.
    return [null, new Error(String(error))];
  }
}

export type ModelData<T extends LLMService> = {
  id: string;
  object: "model";
  created: number;
  owned_by: LLMService;
  permission: [
    {
      id: `modelperm-${string}`;
      object: "model_permission";
      created: string;
      organization: "*";
      group: null;
      is_blocking: false;
    },
  ];
  root: LLMService;
  parent: null;
};

export function createModelList<T extends LLMService>(
  models: string[],
  service: T,
  filterFn: (model: string) => boolean = () => true
) {
  return models.filter(filterFn).map(
    (id): ModelData<T> => ({
      id,
      object: "model",
      created: new Date().getTime(),
      owned_by: service,
      permission: [
        {
          id: `modelperm-${id}`,
          object: "model_permission",
          created: new Date().getTime().toString(),
          organization: "*",
          group: null,
          is_blocking: false,
        },
      ],
      root: service,
      parent: null,
    })
  );
}
