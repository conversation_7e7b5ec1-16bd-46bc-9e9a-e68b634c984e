{"level":30,"time":1753730588987,"pid":15348,"module":"server","module":"key-provider","service":"openai","keyCount":3,"msg":"Loaded OpenAI keys."}
{"level":40,"time":1753730588988,"pid":15348,"module":"server","module":"key-provider","service":"anthropic","msg":"ANTHROPIC_KEYS is not set. Anthropic API will not be available."}
{"level":30,"time":1753730588988,"pid":15348,"module":"server","module":"key-provider","service":"google-ai","keyCount":23,"msg":"Loaded Google AI keys."}
{"level":30,"time":1753730588989,"pid":15348,"module":"server","module":"key-provider","service":"deepseek","keyCount":14,"msg":"Loaded DeepSeek keys."}
{"level":30,"time":1753730588990,"pid":15348,"module":"server","module":"key-provider","service":"xai","keyCount":18,"msg":"Loaded XAI keys."}
{"level":30,"time":1753730588991,"pid":15348,"module":"server","module":"key-provider","service":"groq","keyCount":42,"msg":"Loaded Groq keys."}
{"level":30,"time":1753730588999,"pid":15348,"module":"server","module":"cidr","list":"ADMIN_WHITELIST","ranges":{"ADMIN_WHITELIST":[[{"octets":[0,0,0,0]},0],[{"parts":[0,0,0,0,0,0,0,0]},0]]},"msg":"IP whitelist configured"}
{"level":20,"time":1753730589001,"pid":15348,"module":"server","module":"check-risu-token","msg":"Importing Risu public key"}
{"level":20,"time":1753730589004,"pid":15348,"module":"server","module":"check-risu-token","msg":"Imported Risu public key"}
{"level":30,"time":1753730589035,"pid":15348,"module":"server","module":"cidr","list":"IP_BLACKLIST","ranges":{"IP_BLACKLIST":[]},"msg":"IP blacklist configured"}
{"level":30,"time":1753730589037,"pid":15348,"module":"server","msg":"Server starting up..."}
{"level":30,"time":1753730589205,"pid":15348,"module":"server","build":"30692c3 (modified) (main@yae-miko/ai-reverse-proxy)","status":"M src/admin/web/views/admin_index.ejs","changes":true,"msg":"Got build info from Git."}
{"level":30,"time":1753730589206,"pid":15348,"module":"server","msg":"Checking configs and external dependencies..."}
{"level":30,"time":1753730589222,"pid":15348,"module":"server","rule":"0 4,12,20 * * *","next":"2025-07-29T04:00:00.000+07:00","msg":"Scheduled periodic key recheck job"}
{"level":30,"time":1753730589617,"pid":15348,"module":"server","module":"database","msg":"Initializing database SQLite..."}
{"level":20,"time":1753730589652,"pid":15348,"module":"server","module":"database","msg":"Query: \n\t\t\tCREATE TABLE IF NOT EXISTS \"__drizzle_migrations\" (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t"}
{"level":20,"time":1753730589653,"pid":15348,"module":"server","module":"database","msg":"Query: SELECT id, hash, created_at FROM \"__drizzle_migrations\" ORDER BY created_at DESC LIMIT 1"}
{"level":20,"time":1753730589653,"pid":15348,"module":"server","module":"database","msg":"Query: BEGIN"}
{"level":20,"time":1753730589653,"pid":15348,"module":"server","module":"database","msg":"Query: COMMIT"}
{"level":30,"time":1753730589653,"pid":15348,"module":"server","module":"database","msg":"Database SQLite initialized."}
{"level":30,"time":1753730589653,"pid":15348,"module":"server","msg":"Starting request queue..."}
{"level":10,"time":1753730589654,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730589654,"pid":15348,"module":"server","module":"request-queue","msg":"Started request queue."}
{"level":30,"time":1753730589673,"pid":15348,"module":"server","build":"30692c3 (modified) (main@yae-miko/ai-reverse-proxy)","msg":"Startup complete."}
{"level":30,"time":1753730589692,"pid":15348,"module":"server","port":7860,"interface":"0.0.0.0","msg":"Server ready to accept connections."}
{"level":10,"time":1753730609675,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730615561,"pid":15348,"module":"server","reqId":1,"req":{"id":1,"method":"GET","url":"/admin/","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":302,"headers":{"access-control-allow-origin":"*","location":"/admin/login","vary":"Accept","content-type":"text/html; charset=utf-8","content-length":"41","set-cookie":"********"}},"responseTime":31,"msg":"Request complete - GET \u001b[32m302\u001b[39m / - 31ms"}
{"level":30,"time":1753730615592,"pid":15348,"module":"server","reqId":2,"req":{"id":2,"method":"GET","url":"/admin/login","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"5462","etag":"W/\"1556-VAwNKFYVGP7bNoQ2ozSyFWw4Kek\""}},"responseTime":18,"msg":"Request complete - GET \u001b[32m200\u001b[39m /login - 18ms"}
{"level":30,"time":1753730615689,"pid":15348,"module":"server","reqId":3,"req":{"id":3,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/login","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":28,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 28ms"}
{"level":30,"time":1753730615884,"pid":15348,"module":"server","reqId":4,"req":{"id":4,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/login","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":2,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 2ms"}
{"level":30,"time":1753730628981,"pid":15348,"module":"server","reqId":5,"req":{"id":5,"method":"POST","url":"/admin/login","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","content-type":"application/x-www-form-urlencoded","content-length":"218","origin":"http://localhost:7860","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=0"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","date":"Mon, 28 Jul 2025 19:23:48 GMT","content-type":"application/json; charset=utf-8","content-length":"46","etag":"W/\"2e-sbmaP1h2c7UAB4xf01/jiXyQKIA\"","set-cookie":"********","x-ratelimit-limit":"10","x-ratelimit-remaining":"9","x-ratelimit-reset":"1753731529"}},"responseTime":31,"msg":"Request complete - POST \u001b[32m200\u001b[39m /login - 31ms"}
{"level":30,"time":1753730629038,"pid":15348,"module":"server","reqId":6,"req":{"id":6,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/login","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45496","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":14,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 14ms"}
{"level":20,"time":1753730629429,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730629461,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730629478,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730629559,"pid":15348,"module":"server","reqId":7,"req":{"id":7,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":135,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 135ms"}
{"level":20,"time":1753730629597,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730629611,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753125829]"}
{"level":20,"time":1753730629611,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644229]"}
{"level":20,"time":1753730629612,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138629]"}
{"level":20,"time":1753730629612,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730629613,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730629632,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":10,"time":1753730629695,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730629857,"pid":15348,"module":"server","reqId":8,"req":{"id":8,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"332","etag":"W/\"14c-q3sqPJo9PhbDsri6PQtm1Ss33io\""}},"responseTime":294,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 294ms"}
{"level":30,"time":1753730646463,"pid":15348,"module":"server","reqId":9,"req":{"id":9,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","if-none-match":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\"","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":14,"msg":"Request complete - GET \u001b[32m304\u001b[39m / - 14ms"}
{"level":20,"time":1753730646840,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730646848,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730646856,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730646916,"pid":15348,"module":"server","reqId":10,"req":{"id":10,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":79,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chat/logs - 79ms"}
{"level":20,"time":1753730646933,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730646955,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753125846]"}
{"level":20,"time":1753730646957,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644246]"}
{"level":20,"time":1753730646957,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138646]"}
{"level":20,"time":1753730646958,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730646958,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730646965,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730647074,"pid":15348,"module":"server","reqId":11,"req":{"id":11,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"14c-q3sqPJo9PhbDsri6PQtm1Ss33io\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"332","etag":"W/\"14c-rRMzXQwXSLRdkJcQ/j6v23SlHn8\""}},"responseTime":160,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 160ms"}
{"level":20,"time":1753730648996,"pid":15348,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753730648996,"pid":15348,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753730649689,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753730669690,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753730689690,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730691561,"pid":15348,"module":"server","reqId":12,"req":{"id":12,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45496","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":12,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 12ms"}
{"level":20,"time":1753730691759,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730691765,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730691769,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730691841,"pid":15348,"module":"server","reqId":13,"req":{"id":13,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":84,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chat/logs - 84ms"}
{"level":20,"time":1753730691845,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730691860,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753125891]"}
{"level":20,"time":1753730691861,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644291]"}
{"level":20,"time":1753730691861,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138691]"}
{"level":20,"time":1753730691861,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730691861,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730691877,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730691935,"pid":15348,"module":"server","reqId":14,"req":{"id":14,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"14c-rRMzXQwXSLRdkJcQ/j6v23SlHn8\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-hXcXrPt8+sFwaVDcUnTCrlU+pBU\""}},"responseTime":96,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 96ms"}
{"level":30,"time":1753730703380,"pid":15348,"module":"server","reqId":15,"req":{"id":15,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","if-none-match":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\"","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":8,"msg":"Request complete - GET \u001b[32m304\u001b[39m / - 8ms"}
{"level":20,"time":1753730703561,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730703570,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730703572,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730703637,"pid":15348,"module":"server","reqId":16,"req":{"id":16,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":78,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chat/logs - 78ms"}
{"level":20,"time":1753730703647,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730703676,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753125903]"}
{"level":20,"time":1753730703677,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644303]"}
{"level":20,"time":1753730703677,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138703]"}
{"level":20,"time":1753730703678,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730703678,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730703692,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730703737,"pid":15348,"module":"server","reqId":17,"req":{"id":17,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"14f-hXcXrPt8+sFwaVDcUnTCrlU+pBU\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-PGiL4y5Ux0zYnnB3ZmkS21JxR4M\""}},"responseTime":108,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 108ms"}
{"level":20,"time":1753730708986,"pid":15348,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753730708986,"pid":15348,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753730709702,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730720388,"pid":15348,"module":"server","reqId":18,"req":{"id":18,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45496","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":28,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 28ms"}
{"level":30,"time":1753730720504,"pid":15348,"module":"server","reqId":19,"req":{"id":19,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":21,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 21ms"}
{"level":20,"time":1753730720757,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730720764,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730720766,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730720812,"pid":15348,"module":"server","reqId":20,"req":{"id":20,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":56,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 56ms"}
{"level":20,"time":1753730720826,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730720840,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753125920]"}
{"level":20,"time":1753730720841,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644320]"}
{"level":20,"time":1753730720843,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138720]"}
{"level":20,"time":1753730720844,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730720844,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730720883,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730721027,"pid":15348,"module":"server","reqId":21,"req":{"id":21,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-a4pyZEwkKG0rk6cKGjtFbKEqa58\""}},"responseTime":216,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 216ms"}
{"level":30,"time":1753730721061,"pid":15348,"module":"server","reqId":22,"req":{"id":22,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":4,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 4ms"}
{"level":10,"time":1753730729717,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730742603,"pid":15348,"module":"server","reqId":23,"req":{"id":23,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45496","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":7,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 7ms"}
{"level":30,"time":1753730742676,"pid":15348,"module":"server","reqId":24,"req":{"id":24,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":2,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 2ms"}
{"level":20,"time":1753730742786,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730742790,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730742792,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730742837,"pid":15348,"module":"server","reqId":25,"req":{"id":25,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":54,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 54ms"}
{"level":20,"time":1753730742842,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730742855,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753125942]"}
{"level":20,"time":1753730742855,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644342]"}
{"level":20,"time":1753730742855,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138742]"}
{"level":20,"time":1753730742860,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730742860,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730742872,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730742923,"pid":15348,"module":"server","reqId":26,"req":{"id":26,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-zGbfVaEWc2ZRD+QV5eihrDIENfo\""}},"responseTime":88,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 88ms"}
{"level":30,"time":1753730742958,"pid":15348,"module":"server","reqId":27,"req":{"id":27,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":0,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 0ms"}
{"level":10,"time":1753730749718,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1753730768987,"pid":15348,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753730768987,"pid":15348,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753730769722,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753730789724,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753730809725,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730816529,"pid":15348,"module":"server","reqId":28,"req":{"id":28,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45496","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":8,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 8ms"}
{"level":30,"time":1753730816660,"pid":15348,"module":"server","reqId":29,"req":{"id":29,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":4,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 4ms"}
{"level":20,"time":1753730816857,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730816864,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730816866,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730816906,"pid":15348,"module":"server","reqId":30,"req":{"id":30,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":50,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 50ms"}
{"level":20,"time":1753730816909,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730816919,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753126016]"}
{"level":20,"time":1753730816919,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644416]"}
{"level":20,"time":1753730816919,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138816]"}
{"level":20,"time":1753730816920,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730816920,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730816940,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730816994,"pid":15348,"module":"server","reqId":31,"req":{"id":31,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-WkcZUSrv8LycyrgK4OldTsx/BSQ\""}},"responseTime":89,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 89ms"}
{"level":30,"time":1753730817016,"pid":15348,"module":"server","reqId":32,"req":{"id":32,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":1,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 1ms"}
{"level":20,"time":1753730828998,"pid":15348,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753730828998,"pid":15348,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753730829746,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730835018,"pid":15348,"module":"server","reqId":33,"req":{"id":33,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45496","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":5,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 5ms"}
{"level":30,"time":1753730835155,"pid":15348,"module":"server","reqId":34,"req":{"id":34,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":2,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 2ms"}
{"level":20,"time":1753730835371,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730835379,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730835382,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730835423,"pid":15348,"module":"server","reqId":35,"req":{"id":35,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":54,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 54ms"}
{"level":20,"time":1753730835441,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730835449,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753126035]"}
{"level":20,"time":1753730835450,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644435]"}
{"level":20,"time":1753730835450,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138835]"}
{"level":20,"time":1753730835450,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730835450,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730835457,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730835485,"pid":15348,"module":"server","reqId":36,"req":{"id":36,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"334","etag":"W/\"14e-Vkhz7yJtTpYcWRaXcK96AhRuuqc\""}},"responseTime":49,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 49ms"}
{"level":30,"time":1753730835500,"pid":15348,"module":"server","reqId":37,"req":{"id":37,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":0,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 0ms"}
{"level":30,"time":1753730839183,"pid":15348,"module":"server","reqId":38,"req":{"id":38,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45496","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":7,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 7ms"}
{"level":30,"time":1753730839314,"pid":15348,"module":"server","reqId":39,"req":{"id":39,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":7,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 7ms"}
{"level":20,"time":1753730839435,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730839442,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730839444,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730839484,"pid":15348,"module":"server","reqId":40,"req":{"id":40,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":50,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 50ms"}
{"level":20,"time":1753730839487,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730839498,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753126039]"}
{"level":20,"time":1753730839498,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644439]"}
{"level":20,"time":1753730839498,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138839]"}
{"level":20,"time":1753730839498,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730839499,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730839516,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730839571,"pid":15348,"module":"server","reqId":41,"req":{"id":41,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-m74kT4xpH89sf1TOI6m9/r3fz38\""}},"responseTime":88,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 88ms"}
{"level":30,"time":1753730839599,"pid":15348,"module":"server","reqId":42,"req":{"id":42,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":0,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 0ms"}
{"level":10,"time":1753730849746,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730849971,"pid":15348,"module":"server","reqId":43,"req":{"id":43,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45496","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":7,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 7ms"}
{"level":30,"time":1753730850087,"pid":15348,"module":"server","reqId":44,"req":{"id":44,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":3,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 3ms"}
{"level":20,"time":1753730850200,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730850205,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730850207,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730850257,"pid":15348,"module":"server","reqId":45,"req":{"id":45,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":58,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 58ms"}
{"level":20,"time":1753730850267,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730850275,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753126050]"}
{"level":20,"time":1753730850275,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644450]"}
{"level":20,"time":1753730850275,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138850]"}
{"level":20,"time":1753730850276,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730850276,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730850289,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730850337,"pid":15348,"module":"server","reqId":46,"req":{"id":46,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-E1yoNZd7sbm+3Y8Dy5k7m1PKcEU\""}},"responseTime":87,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 87ms"}
{"level":30,"time":1753730850388,"pid":15348,"module":"server","reqId":47,"req":{"id":47,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":7,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 7ms"}
{"level":30,"time":1753730854506,"pid":15348,"module":"server","reqId":48,"req":{"id":48,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45496","etag":"W/\"b1b8-mooJbWv/i+dKmrGGPXgQnjv71IQ\""}},"responseTime":5,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 5ms"}
{"level":30,"time":1753730854622,"pid":15348,"module":"server","reqId":49,"req":{"id":49,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":3,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 3ms"}
{"level":20,"time":1753730854736,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730854743,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730854745,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730854786,"pid":15348,"module":"server","reqId":50,"req":{"id":50,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":52,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 52ms"}
{"level":20,"time":1753730854789,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730854798,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753126054]"}
{"level":20,"time":1753730854798,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644454]"}
{"level":20,"time":1753730854798,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138854]"}
{"level":20,"time":1753730854799,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730854800,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730854832,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730854885,"pid":15348,"module":"server","reqId":51,"req":{"id":51,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-6ag9UP57VzrDUAd9opnjE5cHUTs\""}},"responseTime":99,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 99ms"}
{"level":30,"time":1753730854932,"pid":15348,"module":"server","reqId":52,"req":{"id":52,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":0,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 0ms"}
{"level":10,"time":1753730869738,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1753730889021,"pid":15348,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753730889021,"pid":15348,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753730889734,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730901233,"pid":15348,"module":"server","reqId":53,"req":{"id":53,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45518","etag":"W/\"b1ce-pZvLamb/s4fwbkhB/NtsthXrCcc\""}},"responseTime":11,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 11ms"}
{"level":30,"time":1753730901339,"pid":15348,"module":"server","reqId":54,"req":{"id":54,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":2,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 2ms"}
{"level":20,"time":1753730901735,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730901742,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730901745,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730901786,"pid":15348,"module":"server","reqId":55,"req":{"id":55,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":54,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 54ms"}
{"level":20,"time":1753730901789,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730901799,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753126101]"}
{"level":20,"time":1753730901800,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644501]"}
{"level":20,"time":1753730901800,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138901]"}
{"level":20,"time":1753730901800,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730901800,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730901812,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730901858,"pid":15348,"module":"server","reqId":56,"req":{"id":56,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-P75iNT47I9BKuc/mhhbnBd7OKAg\""}},"responseTime":73,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 73ms"}
{"level":30,"time":1753730901884,"pid":15348,"module":"server","reqId":57,"req":{"id":57,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":1,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 1ms"}
{"level":10,"time":1753730909737,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730924098,"pid":15348,"module":"server","reqId":58,"req":{"id":58,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45540","etag":"W/\"b1e4-nluhmxeLHAJVtzWknbxNm+Oq+WM\""}},"responseTime":8,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 8ms"}
{"level":30,"time":1753730924210,"pid":15348,"module":"server","reqId":59,"req":{"id":59,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":8,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 8ms"}
{"level":20,"time":1753730924364,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730924370,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730924374,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730924429,"pid":15348,"module":"server","reqId":60,"req":{"id":60,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":69,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 69ms"}
{"level":20,"time":1753730924432,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730924446,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753126124]"}
{"level":20,"time":1753730924447,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644524]"}
{"level":20,"time":1753730924447,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138924]"}
{"level":20,"time":1753730924447,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730924448,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730924482,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730924544,"pid":15348,"module":"server","reqId":61,"req":{"id":61,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-bkmLXv3oKANUaAbdC0xRmjhxbak\""}},"responseTime":117,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 117ms"}
{"level":30,"time":1753730924562,"pid":15348,"module":"server","reqId":62,"req":{"id":62,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":1,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 1ms"}
{"level":10,"time":1753730929733,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730941663,"pid":15348,"module":"server","reqId":63,"req":{"id":63,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45540","etag":"W/\"b1e4-nluhmxeLHAJVtzWknbxNm+Oq+WM\""}},"responseTime":15,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 15ms"}
{"level":30,"time":1753730941978,"pid":15348,"module":"server","reqId":64,"req":{"id":64,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":2,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 2ms"}
{"level":20,"time":1753730942155,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730942171,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730942176,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730942343,"pid":15348,"module":"server","reqId":65,"req":{"id":65,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":191,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 191ms"}
{"level":20,"time":1753730942347,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730942368,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753126142]"}
{"level":20,"time":1753730942369,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644542]"}
{"level":20,"time":1753730942370,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138942]"}
{"level":20,"time":1753730942370,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730942376,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730942393,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730942544,"pid":15348,"module":"server","reqId":66,"req":{"id":66,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-49aBf+IQLeSpwlIF0gvnrRoAIUg\""}},"responseTime":390,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 390ms"}
{"level":30,"time":1753730942547,"pid":15348,"module":"server","reqId":67,"req":{"id":67,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":1,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 1ms"}
{"level":20,"time":1753730949028,"pid":15348,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753730949028,"pid":15348,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753730949735,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753730969734,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730986444,"pid":15348,"module":"server","reqId":68,"req":{"id":68,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","priority":"u=0, i","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"45540","etag":"W/\"b1e4-nluhmxeLHAJVtzWknbxNm+Oq+WM\""}},"responseTime":9,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 9ms"}
{"level":30,"time":1753730986578,"pid":15348,"module":"server","reqId":69,"req":{"id":69,"method":"GET","url":"/res/css/output.css","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/css,*/*;q=0.1","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"style","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=2","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","accept-ranges":"bytes","cache-control":"public, max-age=7200","last-modified":"Thu, 10 Jul 2025 06:41:04 GMT","content-type":"text/css; charset=UTF-8","content-length":"26743"}},"responseTime":7,"msg":"Request complete - GET \u001b[32m200\u001b[39m /css/output.css - 7ms"}
{"level":20,"time":1753730986846,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730986852,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753730986854,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753730986909,"pid":15348,"module":"server","reqId":70,"req":{"id":70,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"1249038","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":64,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chat/logs - 64ms"}
{"level":20,"time":1753730986913,"pid":15348,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753730986925,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753126186]"}
{"level":20,"time":1753730986926,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644586]"}
{"level":20,"time":1753730986926,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751138986]"}
{"level":20,"time":1753730986926,"pid":15348,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753730986927,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753730986937,"pid":15348,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753730986979,"pid":15348,"module":"server","reqId":71,"req":{"id":71,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2183},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-J3egvDIc5W1VAG1sKdrmM9NTeJE\""}},"responseTime":71,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 71ms"}
{"level":30,"time":1753730987013,"pid":15348,"module":"server","reqId":72,"req":{"id":72,"method":"GET","url":"/favicon.ico","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/manage","cookie":"********","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","priority":"u=6","pragma":"no-cache","cache-control":"no-cache"},"remoteAddress":"127.0.0.1","remotePort":2158},"res":{"statusCode":404,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"33","etag":"W/\"21-j0CQ1nlha5JFXTEoGDnnHg2XBY0\""}},"responseTime":0,"msg":"Request failed - GET \u001b[31m404\u001b[39m /favicon.ico - 0ms"}
{"level":10,"time":1753730989759,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1753731009014,"pid":15348,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753731009014,"pid":15348,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753731009760,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731029749,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731049763,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1753731069019,"pid":15348,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753731069019,"pid":15348,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753731069751,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731089750,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731109770,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753731117978,"pid":13964,"module":"server","module":"key-provider","service":"openai","keyCount":3,"msg":"Loaded OpenAI keys."}
{"level":40,"time":1753731117979,"pid":13964,"module":"server","module":"key-provider","service":"anthropic","msg":"ANTHROPIC_KEYS is not set. Anthropic API will not be available."}
{"level":30,"time":1753731117982,"pid":13964,"module":"server","module":"key-provider","service":"google-ai","keyCount":23,"msg":"Loaded Google AI keys."}
{"level":30,"time":1753731117983,"pid":13964,"module":"server","module":"key-provider","service":"deepseek","keyCount":14,"msg":"Loaded DeepSeek keys."}
{"level":30,"time":1753731117985,"pid":13964,"module":"server","module":"key-provider","service":"xai","keyCount":18,"msg":"Loaded XAI keys."}
{"level":30,"time":1753731117987,"pid":13964,"module":"server","module":"key-provider","service":"groq","keyCount":42,"msg":"Loaded Groq keys."}
{"level":30,"time":1753731117997,"pid":13964,"module":"server","module":"cidr","list":"ADMIN_WHITELIST","ranges":{"ADMIN_WHITELIST":[[{"octets":[0,0,0,0]},0],[{"parts":[0,0,0,0,0,0,0,0]},0]]},"msg":"IP whitelist configured"}
{"level":20,"time":1753731117999,"pid":13964,"module":"server","module":"check-risu-token","msg":"Importing Risu public key"}
{"level":20,"time":1753731118000,"pid":13964,"module":"server","module":"check-risu-token","msg":"Imported Risu public key"}
{"level":30,"time":1753731118066,"pid":13964,"module":"server","module":"cidr","list":"IP_BLACKLIST","ranges":{"IP_BLACKLIST":[]},"msg":"IP blacklist configured"}
{"level":30,"time":1753731118068,"pid":13964,"module":"server","msg":"Server starting up..."}
{"level":30,"time":1753731118478,"pid":13964,"module":"server","build":"30692c3 (modified) (main@yae-miko/ai-reverse-proxy)","status":"M src/admin/api/events.ts\n M src/admin/web/views/admin_index.ejs","changes":true,"msg":"Got build info from Git."}
{"level":30,"time":1753731118479,"pid":13964,"module":"server","msg":"Checking configs and external dependencies..."}
{"level":30,"time":1753731118526,"pid":13964,"module":"server","rule":"0 4,12,20 * * *","next":"2025-07-29T04:00:00.000+07:00","msg":"Scheduled periodic key recheck job"}
{"level":30,"time":1753731119213,"pid":13964,"module":"server","module":"database","msg":"Initializing database SQLite..."}
{"level":20,"time":1753731119218,"pid":13964,"module":"server","module":"database","msg":"Query: \n\t\t\tCREATE TABLE IF NOT EXISTS \"__drizzle_migrations\" (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t"}
{"level":20,"time":1753731119218,"pid":13964,"module":"server","module":"database","msg":"Query: SELECT id, hash, created_at FROM \"__drizzle_migrations\" ORDER BY created_at DESC LIMIT 1"}
{"level":20,"time":1753731119219,"pid":13964,"module":"server","module":"database","msg":"Query: BEGIN"}
{"level":20,"time":1753731119219,"pid":13964,"module":"server","module":"database","msg":"Query: COMMIT"}
{"level":30,"time":1753731119219,"pid":13964,"module":"server","module":"database","msg":"Database SQLite initialized."}
{"level":30,"time":1753731119219,"pid":13964,"module":"server","msg":"Starting request queue..."}
{"level":10,"time":1753731119220,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753731119220,"pid":13964,"module":"server","module":"request-queue","msg":"Started request queue."}
{"level":30,"time":1753731119225,"pid":13964,"module":"server","build":"30692c3 (modified) (main@yae-miko/ai-reverse-proxy)","msg":"Startup complete."}
{"level":30,"time":1753731119245,"pid":13964,"module":"server","port":7860,"interface":"0.0.0.0","msg":"Server ready to accept connections."}
{"level":10,"time":1753731139247,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731159237,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1753731178025,"pid":13964,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753731178037,"pid":13964,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753731179249,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731199289,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731219288,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1753731238020,"pid":13964,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753731238022,"pid":13964,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753731239290,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731259296,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731279321,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1753731298016,"pid":13964,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753731298016,"pid":13964,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753731299325,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731319335,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753731322653,"pid":13964,"module":"server","reqId":1,"req":{"id":1,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","if-none-match":"W/\"b1e4-nluhmxeLHAJVtzWknbxNm+Oq+WM\"","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":2681},"res":{"statusCode":302,"headers":{"access-control-allow-origin":"*","location":"/admin/login?redirect=/admin/manage","vary":"Accept","content-type":"text/html; charset=utf-8","content-length":"64","set-cookie":"********"}},"responseTime":13,"msg":"Request complete - GET \u001b[32m302\u001b[39m /manage - 13ms"}
{"level":30,"time":1753731322685,"pid":13964,"module":"server","reqId":2,"req":{"id":2,"method":"GET","url":"/admin/login?redirect=/admin/manage","query":{"redirect":"/admin/manage"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":2681},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"5475","etag":"W/\"1563-mMuLoJS3qp4VmeW4rMusFu4Kc04\"","set-cookie":"********"}},"responseTime":15,"msg":"Request complete - GET \u001b[32m200\u001b[39m /login?redirect=/admin/manage - 15ms"}
{"level":30,"time":1753731325322,"pid":13964,"module":"server","reqId":3,"req":{"id":3,"method":"POST","url":"/admin/login","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/login?redirect=/admin/manage","content-type":"application/x-www-form-urlencoded","content-length":"235","origin":"http://localhost:7860","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=0"},"remoteAddress":"127.0.0.1","remotePort":2681},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","date":"Mon, 28 Jul 2025 19:35:25 GMT","content-type":"application/json; charset=utf-8","content-length":"46","etag":"W/\"2e-sbmaP1h2c7UAB4xf01/jiXyQKIA\"","set-cookie":"********","x-ratelimit-limit":"10","x-ratelimit-remaining":"9","x-ratelimit-reset":"1753732226"}},"responseTime":22,"msg":"Request complete - POST \u001b[32m200\u001b[39m /login - 22ms"}
{"level":30,"time":1753731325537,"pid":13964,"module":"server","reqId":4,"req":{"id":4,"method":"GET","url":"/admin/manage","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","dnt":"1","sec-gpc":"1","connection":"keep-alive","referer":"http://localhost:7860/admin/login?redirect=/admin/manage","cookie":"********","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i"},"remoteAddress":"127.0.0.1","remotePort":2681},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"text/html; charset=utf-8","content-length":"46373","etag":"W/\"b525-wZGrxACGNywlW+NZ717t8DjRQ58\""}},"responseTime":18,"msg":"Request complete - GET \u001b[32m200\u001b[39m / - 18ms"}
{"level":20,"time":1753731325732,"pid":13964,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753731325750,"pid":13964,"module":"server","module":"database","msg":"Query: select COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END) from \"users-events\""}
{"level":20,"time":1753731325762,"pid":13964,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc limit ? -- params: [\"chat-completion\", 100000]"}
{"level":30,"time":1753731325853,"pid":13964,"module":"server","reqId":6,"req":{"id":6,"method":"GET","url":"/admin/api/events/chat/logs","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","if-none-match":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\"","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2683},"res":{"statusCode":304,"headers":{"access-control-allow-origin":"*","etag":"W/\"130f0e-lJklcyWJs80PQbGaVFeI9YBrKkY\""}},"responseTime":126,"msg":"Request complete - GET \u001b[32m304\u001b[39m /chat/logs - 126ms"}
{"level":20,"time":1753731325871,"pid":13964,"module":"server","module":"database","msg":"Query: select distinct json_extract(payload, '$.ip') from \"users-events\""}
{"level":20,"time":1753731325886,"pid":13964,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":20,"time":1753731325932,"pid":13964,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753126525]"}
{"level":20,"time":1753731325933,"pid":13964,"module":"server","module":"database","msg":"Query: select \"payload\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753731326003,"pid":13964,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1753644925]"}
{"level":20,"time":1753731326005,"pid":13964,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\" where lastUsedAt >= ? -- params: [1751139325]"}
{"level":20,"time":1753731326006,"pid":13964,"module":"server","module":"database","msg":"Query: select COUNT(token) from \"users\""}
{"level":20,"time":1753731326009,"pid":13964,"module":"server","module":"database","msg":"Query: select \"payload\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? -- params: [\"chat-completion\"]"}
{"level":20,"time":1753731326022,"pid":13964,"module":"server","module":"database","msg":"Query: select \"payload\", \"userToken\", \"createdAt\", json_extract(\"payload\", '$.inputTokens') as \"inputTokens\", json_extract(\"payload\", '$.outputTokens') as \"outputTokens\", json_extract(\"payload\", '$.family') as \"modelFamily\", json_extract(\"payload\", '$.model') as \"model\" from \"users-events\" \"users_events\" where \"users_events\".\"type\" = ? order by \"users_events\".\"createdAt\" desc -- params: [\"chat-completion\"]"}
{"level":30,"time":1753731326116,"pid":13964,"module":"server","reqId":5,"req":{"id":5,"method":"GET","url":"/admin/api/events/chart-data?timeRange=all","query":{"timeRange":"all"},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2681},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"754500","etag":"W/\"b8344-QlBkV+h65D4Qkly9UNje07OErk8\""}},"responseTime":400,"msg":"Request complete - GET \u001b[32m200\u001b[39m /chart-data?timeRange=all - 400ms"}
{"level":30,"time":1753731326132,"pid":13964,"module":"server","reqId":7,"req":{"id":7,"method":"GET","url":"/admin/api/events/stats","query":{},"params":{},"headers":{"host":"********","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","accept":"*/*","accept-language":"en-US","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:7860/admin/manage","dnt":"1","sec-gpc":"1","connection":"keep-alive","cookie":"********","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=4"},"remoteAddress":"127.0.0.1","remotePort":2684},"res":{"statusCode":200,"headers":{"access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"335","etag":"W/\"14f-g31qxgCAtVo/VcSZ2dkWfQ0n69A\""}},"responseTime":277,"msg":"Request complete - GET \u001b[32m200\u001b[39m /stats - 277ms"}
{"level":10,"time":1753731339335,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":20,"time":1753731358028,"pid":13964,"module":"server","module":"rate-limit","msg":"Running rate limiter cleaner..."}
{"level":20,"time":1753731358028,"pid":13964,"module":"server","module":"rate-limit","msg":"Cleaned 0 buckets."}
{"level":10,"time":1753731359345,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":10,"time":1753731379357,"pid":13964,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
