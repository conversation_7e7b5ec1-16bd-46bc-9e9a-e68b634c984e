{"level":30,"time":1753730588987,"pid":15348,"module":"server","module":"key-provider","service":"openai","keyCount":3,"msg":"Loaded OpenAI keys."}
{"level":40,"time":1753730588988,"pid":15348,"module":"server","module":"key-provider","service":"anthropic","msg":"ANTHROPIC_KEYS is not set. Anthropic API will not be available."}
{"level":30,"time":1753730588988,"pid":15348,"module":"server","module":"key-provider","service":"google-ai","keyCount":23,"msg":"Loaded Google AI keys."}
{"level":30,"time":1753730588989,"pid":15348,"module":"server","module":"key-provider","service":"deepseek","keyCount":14,"msg":"Loaded DeepSeek keys."}
{"level":30,"time":1753730588990,"pid":15348,"module":"server","module":"key-provider","service":"xai","keyCount":18,"msg":"Loaded XAI keys."}
{"level":30,"time":1753730588991,"pid":15348,"module":"server","module":"key-provider","service":"groq","keyCount":42,"msg":"Loaded Groq keys."}
{"level":30,"time":1753730588999,"pid":15348,"module":"server","module":"cidr","list":"ADMIN_WHITELIST","ranges":{"ADMIN_WHITELIST":[[{"octets":[0,0,0,0]},0],[{"parts":[0,0,0,0,0,0,0,0]},0]]},"msg":"IP whitelist configured"}
{"level":20,"time":1753730589001,"pid":15348,"module":"server","module":"check-risu-token","msg":"Importing Risu public key"}
{"level":20,"time":1753730589004,"pid":15348,"module":"server","module":"check-risu-token","msg":"Imported Risu public key"}
{"level":30,"time":1753730589035,"pid":15348,"module":"server","module":"cidr","list":"IP_BLACKLIST","ranges":{"IP_BLACKLIST":[]},"msg":"IP blacklist configured"}
{"level":30,"time":1753730589037,"pid":15348,"module":"server","msg":"Server starting up..."}
{"level":30,"time":1753730589205,"pid":15348,"module":"server","build":"30692c3 (modified) (main@yae-miko/ai-reverse-proxy)","status":"M src/admin/web/views/admin_index.ejs","changes":true,"msg":"Got build info from Git."}
{"level":30,"time":1753730589206,"pid":15348,"module":"server","msg":"Checking configs and external dependencies..."}
{"level":30,"time":1753730589222,"pid":15348,"module":"server","rule":"0 4,12,20 * * *","next":"2025-07-29T04:00:00.000+07:00","msg":"Scheduled periodic key recheck job"}
{"level":30,"time":1753730589617,"pid":15348,"module":"server","module":"database","msg":"Initializing database SQLite..."}
{"level":20,"time":1753730589652,"pid":15348,"module":"server","module":"database","msg":"Query: \n\t\t\tCREATE TABLE IF NOT EXISTS \"__drizzle_migrations\" (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t"}
{"level":20,"time":1753730589653,"pid":15348,"module":"server","module":"database","msg":"Query: SELECT id, hash, created_at FROM \"__drizzle_migrations\" ORDER BY created_at DESC LIMIT 1"}
{"level":20,"time":1753730589653,"pid":15348,"module":"server","module":"database","msg":"Query: BEGIN"}
{"level":20,"time":1753730589653,"pid":15348,"module":"server","module":"database","msg":"Query: COMMIT"}
{"level":30,"time":1753730589653,"pid":15348,"module":"server","module":"database","msg":"Database SQLite initialized."}
{"level":30,"time":1753730589653,"pid":15348,"module":"server","msg":"Starting request queue..."}
{"level":10,"time":1753730589654,"pid":15348,"module":"server","module":"request-queue","stalledRequests":0,"prunedWaitTimes":0,"msg":"Cleaning up request queue."}
{"level":30,"time":1753730589654,"pid":15348,"module":"server","module":"request-queue","msg":"Started request queue."}
{"level":30,"time":1753730589673,"pid":15348,"module":"server","build":"30692c3 (modified) (main@yae-miko/ai-reverse-proxy)","msg":"Startup complete."}
{"level":30,"time":1753730589692,"pid":15348,"module":"server","port":7860,"interface":"0.0.0.0","msg":"Server ready to accept connections."}
