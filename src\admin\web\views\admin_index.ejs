<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include("partials/admin_shared_header", { title: "Admin Dashboard" }) %>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  </head>

  <body class="bg-background p-4 font-mono text-white">
    <div x-data="adminDashboard" class="mx-auto max-w-7xl">
      <h1 class="mb-8 text-center text-4xl font-bold">AI Reverse Proxy Admin</h1>

      <div class="mb-8 w-full">
        <% if (!usersEnabled) { %>
        <div
          class="border-border-warning bg-background-warning text-warning card mb-4 flex flex-col gap-4 rounded-lg border p-4"
        >
          <span class="font-bold">
            🚨 <code class="w-max">user_token</code> gatekeeper is not enabled.
          </span>

          <p>None of the user management features will do anything.</p>
        </div>
        <% } %>

        <!--  -->
        <% if (!persistenceEnabled) { %>
        <div
          class="border-border-warning bg-background-warning text-warning card mb-4 flex flex-col gap-4 rounded-lg border p-4"
        >
          <span class="font-bold">
            ⚠️ <code>GATEKEEPER_STORE=memory</code> User persistence is not configured.
          </span>

          <p>
            Users data will be lost when the server restarts because persistence is not configured.
            <br />
            Be sure to export your users and import them again after restarting the server if you
            want to keep them. See the
            <a
              target="_blank"
              class="text-[#58a9ff] transition-colors hover:text-[#8bc4ff]"
              href="https://gitgud.io/khanon/oai-reverse-proxy/-/blob/main/docs/user-management.md#firebase-realtime-database"
            >
              User management documentation
            </a>
            to learn how to set up persistence.
          </p>
        </div>
        <% } %>

        <template x-if="message">
          <div class="card border-border-success bg-background-success mb-4 p-4 text-center">
            <p class="text-lg" x-text="message"></p>
          </div>
        </template>
      </div>

      <!-- Global Statistics Section -->
      <div
        x-data="globalStats"
        class="card bg-background border-border mb-8 rounded-lg border p-6 shadow-lg"
      >
        <div class="mb-6 flex items-center justify-between">
          <h2 class="text-2xl font-bold">Global Statistics</h2>

          <div class="flex items-center gap-2">
            <button @click="fetchStats()" class="primary-button" :disabled="isLoading">
              <span x-show="isLoading" class="inline-block animate-spin">🔄</span>
              <span x-text="isLoading ? 'Loading...' : 'Refresh'"></span>
            </button>
          </div>
        </div>

        <!-- Loading state -->
        <div x-show="isLoading" class="flex flex-col items-center justify-center py-12">
          <div
            class="border-accent mb-4 h-12 w-12 animate-spin rounded-full border-4 border-t-transparent"
          ></div>
          <p class="text-lg">Loading statistics...</p>
        </div>

        <!-- Error message -->
        <div
          x-show="error"
          x-text="error"
          class="border-border-error bg-background-error text-color-error mb-4 rounded-lg border p-4"
        ></div>

        <!-- Stats display -->
        <div
          x-show="!isLoading && !error"
          class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4"
        >
          <!-- Most Used Model -->
          <div class="bg-background-secondary rounded-lg p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Most Used Model</h3>
            <div class="flex flex-col items-center justify-center">
              <p
                class="text-accent text-center font-mono text-xl"
                x-text="mostUsedModel?.model || 'N/A'"
              ></p>
              <p class="text-sm" x-show="mostUsedModel?.count">
                <span x-text="mostUsedModel?.count"></span> requests
              </p>
            </div>
          </div>

          <!-- Unique IPs -->
          <div class="bg-background-secondary rounded-lg p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Unique IPs</h3>
            <div class="flex flex-col items-center justify-center">
              <p class="text-accent font-mono text-xl" x-text="formatNumber(ipsCount) || 'N/A'"></p>
            </div>
          </div>

          <!-- Total Prompts -->
          <div class="bg-background-secondary rounded-lg p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Total Prompts</h3>
            <div class="flex flex-col items-center justify-center">
              <p
                class="text-accent font-mono text-xl"
                x-text="formatNumber(totalPrompts) || 'N/A'"
              ></p>
            </div>
          </div>

          <!-- Active Users -->
          <div class="bg-background-secondary rounded-lg p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Active Users</h3>
            <div class="grid grid-cols-2 gap-x-4 gap-y-2 text-center md:grid-cols-2">
              <div class="flex items-center justify-between">
                <span class="text-sm">24h</span>
                <p
                  class="text-accent font-mono text-xl"
                  x-text="formatNumber(activeUsers?.daily)"
                ></p>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm">7d</span>
                <p
                  class="text-accent font-mono text-xl"
                  x-text="formatNumber(activeUsers?.weekly)"
                ></p>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm">30d</span>
                <p
                  class="text-accent font-mono text-xl"
                  x-text="formatNumber(activeUsers?.monthly)"
                ></p>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm">Total</span>
                <p
                  class="text-accent font-mono text-xl"
                  x-text="formatNumber(activeUsers?.total)"
                ></p>
              </div>
            </div>
          </div>

          <!-- Input Tokens -->
          <div class="bg-background-secondary rounded-lg p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Input Tokens</h3>
            <div class="flex flex-col items-center justify-center">
              <p
                class="text-accent font-mono text-xl"
                x-text="formatNumber(inputTokens) || 'N/A'"
              ></p>
              <p class="text-sm" x-show="inputCost">
                <span x-text="formatCost(inputCost)"></span>
              </p>
            </div>
          </div>

          <!-- Output Tokens -->
          <div class="bg-background-secondary rounded-lg p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Output Tokens</h3>
            <div class="flex flex-col items-center justify-center">
              <p
                class="text-accent font-mono text-xl"
                x-text="formatNumber(outputTokens) || 'N/A'"
              ></p>
              <p class="text-sm" x-show="outputCost">
                <span x-text="formatCost(outputCost)"></span>
              </p>
            </div>
          </div>

          <!-- Total Tokens -->
          <div class="bg-background-secondary rounded-lg p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Total Tokens</h3>
            <div class="flex flex-col items-center justify-center">
              <p
                class="text-accent font-mono text-xl"
                x-text="formatNumber(totalTokens) || 'N/A'"
              ></p>
              <p class="text-sm" x-show="totalCost">
                <span x-text="formatCost(totalCost)"></span>
              </p>
            </div>
          </div>

          <!-- Uptime -->
          <div class="bg-background-secondary rounded-lg p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Uptime</h3>
            <div class="flex flex-col items-center justify-center">
              <p class="text-accent font-mono text-xl" x-text="uptime || 'N/A'"></p>
            </div>
          </div>
        </div>
      </div>

      <div class="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2">
        <!-- Users Section -->
        <div class="card bg-background border-border rounded-lg border p-6 shadow-lg">
          <h2 class="mb-4 text-2xl font-bold">Users</h2>
          <div class="grid grid-cols-2 gap-2 text-center">
            <a class="secondary-button p-4" href="<%= proxyBasePath %>admin/manage/list-users">
              List Users
            </a>
            <a class="secondary-button p-4" href="<%= proxyBasePath %>admin/manage/create-user">
              Create User
            </a>
            <a class="secondary-button p-4" href="<%= proxyBasePath %>admin/manage/import-users">
              Import Users
            </a>
            <a class="secondary-button p-4" href="<%= proxyBasePath %>admin/manage/export-users">
              Export Users
            </a>
            <a class="secondary-button p-4" href="<%= proxyBasePath %>admin/manage/list-keys">
              Key Manager
            </a>
            <a class="secondary-button p-4" href="<%= proxyBasePath %>admin/service-info">
              Service Info
            </a>
          </div>
        </div>

        <!-- Maintenance Section -->
        <div class="card bg-background border-border rounded-lg border p-6 shadow-lg">
          <h2 class="mb-4 text-2xl font-bold">Maintenance</h2>

          <form class="w-full">
            <fieldset class="border-border rounded-lg border p-4">
              <legend class="px-2 font-bold">Key Recheck</legend>
              <div class="flex flex-col gap-4">
                <span class="block">
                  Triggers a recheck of all keys without restarting the server.
                </span>

                <button
                  id="recheck-keys"
                  type="button"
                  @click="sendRecheckRequest()"
                  class="secondary-button"
                >
                  Force Key Recheck
                </button>
              </div>
            </fieldset>
          </form>
        </div>
      </div>

      <!-- Usage Chart Section -->
      <div
        x-show="!isLoading && !error && logs && logs.length > 0"
        class="card bg-background border-border mb-8 rounded-lg border p-6 shadow-lg"
      >
        <h2 class="mb-4 text-center text-2xl font-bold">Global Usage Chart</h2>
        <div class="h-[500px] w-full">
          <canvas id="usageChart"></canvas>
        </div>
      </div>

      <!-- Daily Averages Section -->
      <div
        x-show="!isLoading && !error && logs && logs.length > 0"
        class="card bg-background border-border mb-8 rounded-lg border p-6 shadow-lg"
      >
        <h2 class="mb-4 text-center text-2xl font-bold">Daily Averages</h2>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
          <!-- Average Prompts -->
          <div class="bg-background-tertiary border-border rounded-lg border p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Avg Prompts/Day</h3>
            <div class="flex flex-col items-center justify-center">
              <p
                class="text-accent font-mono text-xl"
                x-text="formatNumber(averagePrompts, 2) || 'N/A'"
              ></p>
            </div>
          </div>
          <!-- Average Input Tokens -->
          <div class="bg-background-tertiary border-border rounded-lg border p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Avg Input Tokens/Day</h3>
            <div class="flex flex-col items-center justify-center">
              <p
                class="text-accent font-mono text-xl"
                x-text="formatNumber(averageInputTokens, 0) || 'N/A'"
              ></p>
            </div>
          </div>
          <!-- Average Output Tokens -->
          <div class="bg-background-tertiary border-border rounded-lg border p-4">
            <h3 class="mb-2 text-center text-lg font-bold">Avg Output Tokens/Day</h3>
            <div class="flex flex-col items-center justify-center">
              <p
                class="text-accent font-mono text-xl"
                x-text="formatNumber(averageOutputTokens, 0) || 'N/A'"
              ></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Models Section -->
      <div
        x-show="!isLoading && !error && logs && logs.length > 0"
        class="card bg-background border-border mb-8 rounded-lg border p-6 shadow-lg"
      >
        <h2 class="mb-4 text-center text-2xl font-bold">Top Models</h2>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <template x-for="(model, index) in top4Models" :key="model.model">
            <div
              class="bg-background-tertiary card flex flex-col justify-between gap-3 rounded-lg p-3"
            >
              <div class="flex items-center justify-between">
                <code
                  class="w-max rounded-md bg-gray-800 px-2 py-1 text-xs"
                  x-text="model.model"
                ></code>
                <div class="text-sm font-bold" x-text="'#' + (index + 1)"></div>
              </div>
              <div>
                <div class="flex items-center justify-between">
                  <div class="text-sm" x-text="formatNumber(model.count) + ' requests'"></div>
                  <div class="text-accent text-sm" x-text="model.percentage.toFixed(0) + '%'"></div>
                </div>
                <div class="mt-1 h-2 w-full overflow-hidden rounded-full bg-gray-700">
                  <div class="bg-accent h-full" :style="'width: ' + model.percentage + '%'"></div>
                </div>
              </div>
              <div>
                <div class="flex items-center justify-between text-xs">
                  <div x-text="model.inputPercentage + '% In'"></div>
                  <div x-text="(100 - model.inputPercentage) + '% Out'"></div>
                </div>
                <div class="mt-1 flex h-2 w-full overflow-hidden rounded-full bg-gray-700">
                  <div
                    class="h-full bg-blue-500"
                    :style="'width: ' + model.inputPercentage + '%'"
                  ></div>
                  <div
                    class="h-full bg-purple-500"
                    :style="'width: ' + (100 - model.inputPercentage) + '%'"
                  ></div>
                </div>
                <div class="mt-1 flex items-center justify-between text-xs">
                  <div x-text="formatNumber(model.inputTokens)"></div>
                  <div x-text="formatNumber(model.outputTokens)"></div>
                </div>
              </div>
              <div class="border-border-secondary border-t py-2 pb-0 text-xs">
                <div class="flex justify-between">
                  <span>Input:</span>
                  <span x-text="formatCost(model.inputCost)"></span>
                </div>
                <div class="flex justify-between">
                  <span>Output:</span>
                  <span x-text="formatCost(model.outputCost)"></span>
                </div>
                <div class="flex justify-between font-bold">
                  <span>Total:</span>
                  <span x-text="formatCost(model.totalCost)"></span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- Recent Logs Section -->
      <div class="card bg-background border-border mb-8 rounded-lg border p-6 shadow-lg">
        <div class="mb-6 flex items-center justify-between">
          <h2 class="text-2xl font-bold">Recent Chat Completion Logs</h2>

          <div class="flex items-center gap-2">
            <button @click="fetchLogs()" class="primary-button" :disabled="isLoading">
              <span x-show="isLoading" class="inline-block animate-spin">🔄</span>
              <span x-text="isLoading ? 'Loading...' : 'Refresh'"></span>
            </button>
          </div>
        </div>

        <!-- Loading state -->
        <div x-show="isLoading" class="flex flex-col items-center justify-center py-12">
          <div
            class="border-accent mb-4 h-12 w-12 animate-spin rounded-full border-4 border-t-transparent"
          ></div>
          <p class="text-lg">Loading logs...</p>
        </div>

        <!-- Error message -->
        <div
          x-show="error"
          x-text="error"
          class="border-border-error bg-background-error text-color-error mb-4 rounded-lg border p-4"
        ></div>

        <!-- No logs message -->
        <div
          x-show="!isLoading && !error && (!logs || logs.length === 0)"
          class="py-8 text-center text-gray-400 italic"
        >
          No logs available. Make sure event logging is enabled in your configuration.
        </div>

        <!-- Logs table -->
        <div x-show="!isLoading && !error && logs && logs.length > 0" class="overflow-x-auto">
          <table
            class="border-border bg-background w-full border-separate border-spacing-0 overflow-hidden rounded-lg border"
          >
            <thead class="bg-background-secondary">
              <tr>
                <th class="text-accent border-border border-b p-2 text-left font-bold uppercase">
                  User
                </th>
                <th class="text-accent border-border border-b p-2 text-left font-bold uppercase">
                  Model
                </th>
                <th class="text-accent border-border border-b p-2 text-left font-bold uppercase">
                  IP
                </th>
                <th class="text-accent border-border border-b p-2 text-left font-bold uppercase">
                  Key
                </th>
                <th class="text-accent border-border border-b p-2 text-right font-bold uppercase">
                  Input
                </th>
                <th class="text-accent border-border border-b p-2 text-right font-bold uppercase">
                  Output
                </th>
                <th class="text-accent border-border border-b p-2 text-right font-bold uppercase">
                  Total
                </th>
                <th class="text-accent border-border border-b p-2 text-right font-bold uppercase">
                  Date
                </th>
              </tr>
            </thead>
            <tbody>
              <template x-for="(log, index) in paginatedLogs" :key="index">
                <tr
                  class="transition-colors *:text-xs hover:bg-[rgba(255,255,255,0.1)]"
                  :class="{ 'bg-background-tertiary': index % 2 === 1 }"
                >
                  <td class="border-border border-b p-2">
                    <template x-if="log.userToken.includes('No')">
                      <code class="w-max">No Token</code>
                    </template>

                    <template x-if="!log.userToken.includes('No')">
                      <code>
                        <a
                          class="w-max"
                          :href="`<%= proxyBasePath %>admin/manage/view-user/${log.userToken}`"
                          x-text="'...' + log.userToken.slice(-5)"
                        ></a>
                      </code>
                    </template>
                  </td>

                  <td class="border-border border-b p-2 text-sm">
                    <code x-text="log.payload.model"></code>
                  </td>

                  <td class="border-border border-b p-2 text-sm">
                    <code x-text="formatIp(log.payload.ip)"></code>
                  </td>

                  <td class="border-border border-b p-2 text-sm">
                    <code class="w-max">
                      <a
                        class="w-max"
                        x-text="log.payload.keyHash"
                        :href="`<%= proxyBasePath %>admin/manage/view-key/${getKeyService(log.payload.keyHash)}/${log.payload.keyHash}`"
                      ></a>
                    </code>
                  </td>

                  <td class="border-border border-b p-2">
                    <div
                      class="flex items-center justify-between gap-1"
                      x-bind:title="getPricePerMillion(log.input.cost, log.input.tokens)"
                    >
                      <span
                        class="w-full text-right"
                        x-text="formatNumber(log.input.tokens)"
                      ></span>
                      <span
                        class="text-accent-light text-sm"
                        x-text="formatCost(log.input.cost)"
                      ></span>
                    </div>
                  </td>
                  <td class="border-border border-b p-2">
                    <div
                      class="flex items-center justify-between gap-1"
                      x-bind:title="getPricePerMillion(log.output.cost, log.output.tokens)"
                    >
                      <span
                        class="w-full text-right"
                        x-text="formatNumber(log.output.tokens)"
                      ></span>
                      <span
                        class="text-accent-light text-sm"
                        x-text="formatCost(log.output.cost)"
                      ></span>
                    </div>
                  </td>
                  <td class="border-border border-b p-2">
                    <div class="flex items-center justify-between gap-1">
                      <span
                        class="w-full text-right"
                        x-text="formatNumber(log.input.tokens + log.output.tokens)"
                      ></span>
                      <span
                        class="text-accent-light text-sm"
                        x-text="formatCost(log.input.cost + log.output.cost)"
                      ></span>
                    </div>
                  </td>
                  <td
                    class="border-border border-b p-2 text-right"
                    x-text="formatDate(log.createdAt)"
                  ></td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>

        <!-- Pagination controls -->
        <div class="mt-6 flex justify-center" x-show="logs && logs.length > logsPerPage">
          <div class="flex items-center gap-4">
            <button
              class="primary-button"
              @click="changePage('prev')"
              :disabled="currentPage === 1"
              :class="{ 'bg-[#2a3a4a] opacity-70 cursor-not-allowed': currentPage === 1, 'hover:bg-accent-hover': currentPage !== 1 }"
            >
              &laquo; Prev
            </button>

            <span class="text-base">
              Page <span x-text="currentPage"></span> of
              <span x-text="totalPages"></span>
            </span>

            <button
              class="primary-button"
              @click="changePage('next')"
              :disabled="currentPage === totalPages"
              :class="{ 'bg-[#2a3a4a] opacity-70 cursor-not-allowed': currentPage === totalPages, 'hover:bg-accent-hover': currentPage !== totalPages }"
            >
              Next &raquo;
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener("alpine:init", () => {
        // Global Statistics Component
        Alpine.data("globalStats", () => ({
          isLoading: false,
          error: null,

          // Statistics data
          mostUsedModel: null,
          ipsCount: 0,
          totalPrompts: 0,
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
          inputCost: 0,
          outputCost: 0,
          totalCost: 0,
          uptime: "",
          activeUsers: {
            daily: 0,
            weekly: 0,
            monthly: 0,
            total: 0,
          },

          init() {
            this.fetchStats();
          },

          fetchStats() {
            this.isLoading = true;
            this.error = null;

            // Use the dedicated API endpoint for global statistics
            fetch("/admin/api/events/stats")
              .then((response) => {
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                return response.json();
              })
              .then((data) => {
                if (data.error) {
                  this.error = data.error.message || "Failed to fetch statistics";
                } else {
                  // Set all statistics from the API response
                  this.mostUsedModel = data.mostUsedModel;
                  this.ipsCount = data.ipsCount;
                  this.totalPrompts = data.totalPrompts;
                  this.inputTokens = data.inputTokens;
                  this.outputTokens = data.outputTokens;
                  this.totalTokens = data.totalTokens;
                  this.inputCost = data.inputCost;
                  this.outputCost = data.outputCost;
                  this.totalCost = data.totalCost;
                  this.uptime = data.uptime;
                  this.activeUsers = data.activeUsers;
                }
                this.isLoading = false;
              })
              .catch((err) => {
                this.error = "Failed to fetch statistics: " + err.message;
                this.isLoading = false;
              });
          },

          // Format helpers for displaying statistics

          formatNumber(num) {
            if (num === undefined || num === null) return "0";
            return num.toLocaleString();
          },

          formatCost(cost) {
            if (cost === undefined || cost === null) return "$0.00";
            return "$" + cost.toFixed(2);
          },
        }));

        // Admin Dashboard Component
        Alpine.data("adminDashboard", () => ({
          logs: [],
          isLoading: false,
          error: null,
          message: null,
          currentPage: 1,
          logsPerPage: 30,
          usageChart: null,
          top4Models: [],
          averagePrompts: 0,
          averageInputTokens: 0,
          averageOutputTokens: 0,
          dateFormat: new Intl.DateTimeFormat("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
          }),

          init() {
            this.fetchLogs();
          },

          sendRecheckRequest() {
            fetch("/admin/api/keys/recheck", { method: "POST" })
              .then((response) => {
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                return response.json();
              })
              .then((data) => {
                if (data.error) this.error = data.error.message || "Failed to recheck keys";
                else this.message = `Successfully rechecked ${data.amount} keys.`;
              })
              .catch((err) => {
                this.error = "Failed to recheck keys: " + err.message;
              });
          },

          fetchLogs() {
            this.isLoading = true;
            this.error = null;

            // Fetch all chat completion logs
            fetch("/admin/api/events/chat/logs")
              .then((response) => {
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                return response.json();
              })
              .then((data) => {
                if (data.error) {
                  this.error = data.error.message || "Failed to fetch logs";
                } else {
                  this.logs = data.logs;
                  this.currentPage = 1;
                  this.calculateExtraStats();
                  this.$nextTick(() => this.initChart());
                }
                this.isLoading = false;
              })
              .catch((err) => {
                this.error = "Failed to fetch logs: " + err.message;
                this.isLoading = false;
              });
          },

          formatDate(timestamp) {
            if (!timestamp) return null;
            const date = new Date(timestamp);
            return this.dateFormat.format(date);
          },

          formatNumber(num, precision) {
            if (num === undefined || num === null) return "0";
            if (typeof precision === "number") {
              return num.toLocaleString("en-US", {
                minimumFractionDigits: precision,
                maximumFractionDigits: precision,
              });
            }
            return num.toLocaleString();
          },

          formatCost(cost) {
            if (cost === undefined || cost === null) return "$0.000000";
            return "$" + cost.toFixed(6);
          },

          formatIp(ip) {
            if (!ip) return "";
            if (ip.startsWith("ip-")) return ip.slice(0, 13);
            return ip;
          },

          getKeyService(key) {
            if (key.startsWith("ant-")) return "anthropic";
            if (key.startsWith("plm-")) return "google-ai";
            if (key.startsWith("ds-")) return "deepseek";
            return "openai";
          },

          getPricePerMillion(cost, tokens) {
            if (!tokens || tokens === 0) return "";
            const pricePer1M = ((cost / tokens) * 1_000_000).toFixed(2);

            return `$${pricePer1M}/1M tokens`;
          },

          // Computed properties for pagination
          get paginatedLogs() {
            if (!this.logs || !this.logs.length) return [];
            const startIndex = (this.currentPage - 1) * this.logsPerPage;
            const endIndex = startIndex + this.logsPerPage;
            return this.logs.slice(startIndex, endIndex);
          },

          get totalPages() {
            if (!this.logs || !this.logs.length) return 1;
            return Math.ceil(this.logs.length / this.logsPerPage);
          },

          // Method to change page
          changePage(direction) {
            if (direction === "prev") {
              this.currentPage = Math.max(1, this.currentPage - 1);
            } else if (direction === "next") {
              this.currentPage = Math.min(this.totalPages, this.currentPage + 1);
            }
          },

          calculateExtraStats() {
            if (!this.logs || this.logs.length === 0) return;

            const groupedByDate = this.groupLogsByDate();
            const numDays = Object.keys(groupedByDate).length;
            const modelStats = {};

            this.logs.forEach((log) => {
              const model = log.payload.model;
              if (model) {
                if (!modelStats[model]) {
                  modelStats[model] = {
                    count: 0,
                    inputTokens: 0,
                    outputTokens: 0,
                    inputCost: 0,
                    outputCost: 0,
                  };
                }
                modelStats[model].count++;
                modelStats[model].inputTokens += log.input.tokens || 0;
                modelStats[model].outputTokens += log.output.tokens || 0;
                modelStats[model].inputCost += log.input.cost || 0;
                modelStats[model].outputCost += log.output.cost || 0;
              }
            });

            let totalInputTokens = 0;
            let totalOutputTokens = 0;
            this.logs.forEach((log) => {
              totalInputTokens += log.input.tokens || 0;
              totalOutputTokens += log.output.tokens || 0;
            });

            if (numDays > 0) {
              this.averagePrompts = this.logs.length / numDays;
              this.averageInputTokens = totalInputTokens / numDays;
              this.averageOutputTokens = totalOutputTokens / numDays;
            }

            const totalRequests = this.logs.length;
            this.top4Models = Object.entries(modelStats)
              .map(([model, stats]) => {
                const totalTokens = stats.inputTokens + stats.outputTokens;
                const percentage = totalRequests > 0 ? (stats.count / totalRequests) * 100 : 0;
                const inputPercentage =
                  totalTokens > 0 ? Math.round((stats.inputTokens / totalTokens) * 100) : 0;

                return {
                  model,
                  ...stats,
                  percentage,
                  inputPercentage,
                  totalCost: stats.inputCost + stats.outputCost,
                };
              })
              .sort((a, b) => b.count - a.count)
              .slice(0, 4);
          },

          groupLogsByDate() {
            if (!this.logs || !this.logs.length) return {};

            const grouped = {};

            this.logs.forEach((log) => {
              const date = new Date(log.createdAt);
              const dateStr = date.toISOString().split("T")[0]; // YYYY-MM-DD format

              if (!grouped[dateStr]) {
                grouped[dateStr] = {
                  count: 0,
                  inputTokens: 0,
                  outputTokens: 0,
                  inputCost: 0,
                  outputCost: 0,
                  totalCost: 0,
                };
              }

              grouped[dateStr].count++;
              grouped[dateStr].inputTokens += log.input.tokens || 0;
              grouped[dateStr].outputTokens += log.output.tokens || 0;
              grouped[dateStr].inputCost += log.input.cost || 0;
              grouped[dateStr].outputCost += log.output.cost || 0;
              grouped[dateStr].totalCost += (log.input.cost || 0) + (log.output.cost || 0);
            });

            return grouped;
          },

          initChart() {
            if (this.usageChart) {
              this.usageChart.destroy();
            }

            if (!this.logs || !this.logs.length) return;

            const groupedData = this.groupLogsByDate();
            const dates = Object.keys(groupedData).sort();

            const requestCounts = dates.map((date) => groupedData[date].count);
            const inputTokens = dates.map((date) => groupedData[date].inputTokens);
            const outputTokens = dates.map((date) => groupedData[date].outputTokens);
            const totalCosts = dates.map((date) => groupedData[date].totalCost);

            const formattedDates = dates.map((date) => {
              const [year, month, day] = date.split("-");
              return `${month}/${day}`;
            });

            const ctx = document.getElementById("usageChart");
            if (!ctx) return;

            this.usageChart = new Chart(ctx, {
              type: "bar",
              data: {
                labels: formattedDates,
                datasets: [
                  {
                    label: "Requests",
                    data: requestCounts,
                    backgroundColor: "rgba(54, 162, 235, 0.5)",
                    borderColor: "rgba(54, 162, 235, 1)",
                    borderWidth: 1,
                    yAxisID: "y",
                  },
                  {
                    label: "Input Tokens",
                    data: inputTokens,
                    backgroundColor: "rgba(75, 192, 192, 0.5)",
                    borderColor: "rgba(75, 192, 192, 1)",
                    borderWidth: 1,
                    yAxisID: "y1",
                  },
                  {
                    label: "Output Tokens",
                    data: outputTokens,
                    backgroundColor: "rgba(153, 102, 255, 0.5)",
                    borderColor: "rgba(153, 102, 255, 1)",
                    borderWidth: 1,
                    yAxisID: "y1",
                  },
                  {
                    label: "Total Cost ($)",
                    data: totalCosts,
                    backgroundColor: "rgba(255, 159, 64, 0.5)",
                    borderColor: "rgba(255, 159, 64, 1)",
                    borderWidth: 1,
                    yAxisID: "y2",
                    type: "line",
                    fill: false,
                    tension: 0.4,
                  },
                ],
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    type: "linear",
                    display: true,
                    position: "left",
                    title: { display: true, text: "Requests" },
                    beginAtZero: true,
                  },
                  y1: {
                    type: "linear",
                    display: true,
                    position: "right",
                    title: { display: true, text: "Tokens" },
                    beginAtZero: true,
                    grid: { drawOnChartArea: false },
                  },
                  y2: {
                    type: "linear",
                    display: true,
                    position: "right",
                    title: { display: true, text: "Cost ($)" },
                    beginAtZero: true,
                    grid: { drawOnChartArea: false },
                  },
                },
                plugins: {
                  tooltip: {
                    callbacks: {
                      label: function (context) {
                        let label = context.dataset.label || "";
                        if (label) label += ": ";
                        if (context.parsed.y !== null) {
                          if (label.includes("Cost")) {
                            label += "$" + context.parsed.y.toFixed(6);
                          } else {
                            label += new Intl.NumberFormat().format(context.parsed.y);
                          }
                        }
                        return label;
                      },
                    },
                  },
                },
              },
            });
          },
        }));
      });
    </script>
  </body>
</html>
