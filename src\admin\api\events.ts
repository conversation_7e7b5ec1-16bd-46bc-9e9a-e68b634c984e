import { and, eq, getTableColumns, sql } from "drizzle-orm";
import { subDays } from "date-fns";
import { Router } from "express";
import { z } from "zod/v4";

import { config } from "@/config";
import { buildInfo } from "@/service-info";

import { cacheStore } from "@/shared/cache";
import { getDatabase } from "@/shared/database";
import * as schema from "@/shared/database/schema";
import type { ModelFamily } from "@/shared/models";
import { getTokenCostUsd } from "@/shared/stats";
import { getUserUsages, hashIp } from "@/shared/users/user-store";
import { ObjectTyped } from "@/shared/utils";

const router = Router();

/**
 * Get all chat completion events.
 * GET /admin/api/events/chat/logs
 *
 * @param limit - The number of records to return (default: 100000)
 *
 * @returns The list of events
 */
router.get("/chat/logs", async (req, res) => {
  const limit = z.coerce.number().default(100000).parse(req.query.limit);
  const db = getDatabase();

  if (!config.eventLogging) {
    return res.status(404).json({ error: { message: "Event logging is disabled" } });
  }

  const data = db.query.users_events.findMany({
    where: (fields, { eq }) => eq(fields.type, "chat-completion"),
    orderBy: (fields, { desc }) => desc(fields.createdAt),
    limit,
    columns: { id: false, type: false },
    extras: (field, { sql }) => ({
      inputTokens: sql<number>`json_extract(${field.payload}, '$.inputTokens')`.as("inputTokens"),
      outputTokens: sql<number>`json_extract(${field.payload}, '$.outputTokens')`.as(
        "outputTokens"
      ),
      modelFamily: sql<ModelFamily>`json_extract(${field.payload}, '$.family')`.as("modelFamily"),
    }),
  });

  const ips = await db
    .selectDistinct({ ips: sql<string | null>`json_extract(payload, '$.ip')` })
    .from(schema.users_events);

  const userCount = await db
    .select({
      userCount: sql<number>`COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END)`,
    })
    .from(schema.users_events);

  const ipsCount = new Set(
    ips
      .map(({ ips }) => ips)
      .filter((ips) => ips !== null)
      .map(hashIp)
  );

  const logs = (await data).map((log) => {
    const cost = getTokenCostUsd(log);

    return {
      modelFamily: log.modelFamily,
      input: { tokens: log.inputTokens, cost: cost.input },
      output: { tokens: log.outputTokens, cost: cost.output },
      createdAt: log.createdAt,
      payload: log.payload,
      userToken: log.userToken,
    };
  });

  return res.json({ logs, ipsCount: ipsCount.size, userCount: userCount[0].userCount });
});

/**
 * Returns events for the given user token.
 * GET /admin/api/events/:token/logs
 *
 * @param token - The user token
 * @param type - The type of event to filter by (optional)
 * @param skip - The number of records to skip (default: 0)
 * @param limit - The number of records to return (default: 200)
 * @param sorting - The sorting configuration (default: createdAt asc)
 *
 * @returns The list of events
 */
router.get("/:token/logs", async (req, res) => {
  if (!config.eventLogging) {
    return res.status(404).json({ error: { message: "Event logging is disabled" } });
  }

  const allowSortingKeys = ObjectTyped.keys(getTableColumns(schema.users_events));

  const logSchema = z.object({
    token: z.string(),
    type: z.enum(["new-ip", "user-action", "chat-completion"]).optional(),
    skip: z.coerce.number().default(0),
    limit: z.coerce.number().default(100000),
    sorting: z
      .object({
        table: z.enum(allowSortingKeys),
        direction: z.enum(["asc", "desc"]),
      })
      .default({ direction: "asc", table: "createdAt" }),
  });

  const args = logSchema.safeParse({ ...req.params, ...req.query });
  if (!args.success) {
    return res.status(400).json({ error: args.error });
  }

  const filter = and(
    eq(schema.users_events.userToken, args.data.token),
    args.data.type ? eq(schema.users_events.type, args.data.type) : undefined
  );

  const db = getDatabase();

  const countPromise = db.$count(schema.users_events, filter);
  const dataPromise = db.query.users_events.findMany({
    where: filter,
    limit: args.data.limit,
    offset: args.data.skip,
    orderBy: (_, { asc, desc, sql }) =>
      args.data.sorting.direction === "asc"
        ? asc(sql.raw(args.data.sorting.table))
        : desc(sql.raw(args.data.sorting.table)),
  });

  const [data, count] = await Promise.all([dataPromise, countPromise]);
  return res.json({ count, data });
});

async function getUsageFromEvents(db: ReturnType<typeof getDatabase>) {
  const events = await db.query.users_events.findMany({
    where: (fields, { eq }) => eq(fields.type, "chat-completion"),
    columns: { payload: true },
  });

  let totalPrompts = 0;
  let inputTokens = 0;
  let outputTokens = 0;
  let inputCost = 0;
  let outputCost = 0;

  for (const event of events) {
    totalPrompts++;
    const {
      inputTokens: iTokens,
      outputTokens: oTokens,
      family,
      model,
    } = event.payload as {
      inputTokens: number;
      outputTokens: number;
      family: ModelFamily;
      model: string;
    };
    inputTokens += iTokens;
    outputTokens += oTokens;
    const cost = getTokenCostUsd({
      modelFamily: family,
      inputTokens: iTokens,
      outputTokens: oTokens,
    });
    inputCost += cost.input;
    outputCost += cost.output;
  }

  return {
    totalPrompts,
    inputTokens,
    outputTokens,
    inputCost,
    outputCost,
  };
}

/**
 * Get global statistics for the admin dashboard.
 * GET /admin/api/events/stats
 *
 * @returns Global statistics including most used model, unique IPs, total tokens, costs, etc.
 */
router.get("/stats", async (req, res) => {
  const cachedStats = await cacheStore.get("global-stats");
  if (cachedStats) {
    return res.json(cachedStats);
  }

  const db = getDatabase();

  if (!config.eventLogging) {
    return res.status(404).json({ error: { message: "Event logging is disabled" } });
  }

  try {
    // Get chat completion logs to calculate most used model
    const logsData = db.query.users_events.findMany({
      where: (fields, { eq }) => eq(fields.type, "chat-completion"),
      orderBy: (fields, { desc }) => desc(fields.createdAt),
      columns: { id: false, type: false },
      extras: (field, { sql }) => ({
        inputTokens: sql<number>`json_extract(${field.payload}, '$.inputTokens')`.as("inputTokens"),
        outputTokens: sql<number>`json_extract(${field.payload}, '$.outputTokens')`.as(
          "outputTokens"
        ),
        modelFamily: sql<ModelFamily>`json_extract(${field.payload}, '$.family')`.as("modelFamily"),
        model: sql<string>`json_extract(${field.payload}, '$.model')`.as("model"),
      }),
    });

    // Get unique IPs count
    const ips = await db
      .selectDistinct({ ips: sql<string | null>`json_extract(payload, '$.ip')` })
      .from(schema.users_events);

    const ipsCount = new Set(
      ips
        .map(({ ips }) => ips)
        .filter((ips) => ips !== null)
        .map(hashIp)
    );

    // Get user count
    const sevenDaysAgo = subDays(new Date(), 7);
    const twentyFourHoursAgo = subDays(new Date(), 1);
    const thirtyDaysAgo = subDays(new Date(), 30);

    const activeUsersWeekly = await db
      .select({
        count: sql<number>`COUNT(token)`,
      })
      .from(schema.users)
      .where(sql`lastUsedAt >= ${Math.floor(sevenDaysAgo.getTime() / 1000)}`);

    const activeUsersDaily = await db
      .select({
        count: sql<number>`COUNT(token)`,
      })
      .from(schema.users)
      .where(sql`lastUsedAt >= ${Math.floor(twentyFourHoursAgo.getTime() / 1000)}`);

    const activeUsersMonthly = await db
      .select({
        count: sql<number>`COUNT(token)`,
      })
      .from(schema.users)
      .where(sql`lastUsedAt >= ${Math.floor(thirtyDaysAgo.getTime() / 1000)}`);

    const totalUsers = await db.select({ count: sql<number>`COUNT(token)` }).from(schema.users);

    // Get global usage statistics
    const usageStats =
      config.gatekeeper === "user_token" ? await getUserUsages() : await getUsageFromEvents(db);

    // Calculate most used model
    const logs = await logsData;
    const modelCounts: Record<string, number> = {};
    logs.forEach((log) => {
      const model = log.model;

      if (!modelCounts[model]) modelCounts[model] = 0;
      modelCounts[model]++;
    });

    // Find the model with the highest count
    let mostUsedModel = null;
    let highestCount = 0;

    Object.entries(modelCounts).forEach(([model, count]) => {
      if (count > highestCount) {
        mostUsedModel = model;
        highestCount = count;
      }
    });

    // Get service info for uptime
    const baseUrl = req.protocol + "://" + req.get("host") + config.proxyEndpointRoute;
    const serviceInfo = await buildInfo(baseUrl, true);

    // Combine all statistics
    const stats = {
      mostUsedModel: {
        model: mostUsedModel,
        count: highestCount,
      },
      ipsCount: ipsCount.size,
      activeUsers: {
        daily: activeUsersDaily[0].count,
        weekly: activeUsersWeekly[0].count,
        monthly: activeUsersMonthly[0].count,
        total: totalUsers[0].count,
      },
      uptime: serviceInfo.uptime,
      totalPrompts: usageStats.totalPrompts,
      inputTokens: usageStats.inputTokens,
      outputTokens: usageStats.outputTokens,
      totalTokens: usageStats.inputTokens + usageStats.outputTokens,
      inputCost: usageStats.inputCost,
      outputCost: usageStats.outputCost,
      totalCost: usageStats.inputCost + usageStats.outputCost,
    };

    await cacheStore.set("global-stats", stats, 30 * 1000);

    return res.json(stats);
  } catch (error) {
    req.log.error({ error }, "Failed to fetch global statistics");
    return res.status(500).json({ error: { message: "Failed to fetch global statistics" } });
  }
});

/**
 * Get filtered chart data for admin dashboard.
 * GET /admin/api/events/chart-data
 *
 * @param timeRange - Time range filter (all, 90d, 60d, 30d, 7d, 24h)
 *
 * @returns Filtered chart data and model distribution
 */
router.get("/chart-data", async (req, res) => {
  const timeRange = z
    .enum(["all", "90d", "60d", "30d", "7d", "24h"])
    .default("all")
    .parse(req.query.timeRange);

  // Check cache first
  const cacheKey = `chart-data-${timeRange}`;
  const cachedData = await cacheStore.get(cacheKey);
  if (cachedData) {
    return res.json(cachedData);
  }

  const db = getDatabase();

  if (!config.eventLogging) {
    return res.status(404).json({ error: { message: "Event logging is disabled" } });
  }

  try {
    // Calculate date filter
    let dateFilter = null;
    if (timeRange !== "all") {
      const days = {
        "90d": 90,
        "60d": 60,
        "30d": 30,
        "7d": 7,
        "24h": 1,
      }[timeRange];

      const cutoffDate = subDays(new Date(), days);
      const cutoffTimestamp = Math.floor(cutoffDate.getTime() / 1000);
      dateFilter = sql`createdAt >= ${cutoffTimestamp}`;
    }

    // Build where clause
    const whereClause = dateFilter
      ? and(eq(schema.users_events.type, "chat-completion"), dateFilter)
      : eq(schema.users_events.type, "chat-completion");

    // Get filtered logs for chart data
    const chartLogs = await db.query.users_events.findMany({
      where: whereClause,
      orderBy: (fields, { desc }) => desc(fields.createdAt),
      columns: { id: false, type: false },
      extras: (field, { sql }) => ({
        inputTokens: sql<number>`json_extract(${field.payload}, '$.inputTokens')`.as("inputTokens"),
        outputTokens: sql<number>`json_extract(${field.payload}, '$.outputTokens')`.as(
          "outputTokens"
        ),
        modelFamily: sql<ModelFamily>`json_extract(${field.payload}, '$.family')`.as("modelFamily"),
        model: sql<string>`json_extract(${field.payload}, '$.model')`.as("model"),
      }),
    });

    // Get all logs for model distribution (always show all data)
    const allLogs = await db.query.users_events.findMany({
      where: (fields, { eq }) => eq(fields.type, "chat-completion"),
      columns: { id: false, type: false, createdAt: false, userToken: false },
      extras: (field, { sql }) => ({
        model: sql<string>`json_extract(${field.payload}, '$.model')`.as("model"),
      }),
    });

    // Process chart logs
    const processedChartLogs = chartLogs.map((log) => {
      const cost = getTokenCostUsd(log);
      return {
        modelFamily: log.modelFamily,
        input: { tokens: log.inputTokens, cost: cost.input },
        output: { tokens: log.outputTokens, cost: cost.output },
        createdAt: log.createdAt,
        payload: { model: log.model },
        userToken: log.userToken,
      };
    });

    // Calculate model distribution
    const modelCounts: Record<string, number> = {};
    allLogs.forEach((log) => {
      const model = log.model;
      if (model) {
        modelCounts[model] = (modelCounts[model] || 0) + 1;
      }
    });

    // Sort models by count and get top 10
    const sortedModels = Object.entries(modelCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10);

    const modelDistribution = sortedModels.map(([model, count]) => ({
      model,
      count,
      percentage: ((count / allLogs.length) * 100).toFixed(1),
    }));

    const responseData = {
      chartLogs: processedChartLogs,
      modelDistribution,
      totalRequests: allLogs.length,
      filteredRequests: processedChartLogs.length,
    };

    // Cache for 30 seconds
    await cacheStore.set(cacheKey, responseData, 30 * 1000);

    return res.json(responseData);
  } catch (error) {
    req.log.error({ error }, "Failed to fetch chart data");
    return res.status(500).json({ error: { message: "Failed to fetch chart data" } });
  }
});

export { router as eventsApiRouter };
