import crypto from "crypto";
import type * as http from "http";
import schedule from "node-schedule";
import os from "os";

import { config } from "@/config";
import { logger } from "@/logger";
import { cacheStore } from "../cache";
import { MODEL_FAMILY_SERVICE, type LLMService, type ModelFamily } from "../models";

import { AnthropicKeyProvider, type AnthropicKeyUpdate } from "./anthropic/provider";
import { DeepSeekKeyProvider } from "./deepseek/provider";
import { GoogleAIKeyProvider } from "./google-ai/provider";
import { GroqKeyProvider } from "./groq/provider";
import type { Key, KeyProvider } from "./index";
import { OpenAIKeyProvider, type OpenAIKeyUpdate } from "./openai/provider";
import { XAIKeyProvider } from "./xai/provider";

type AllowedPartial = OpenAIKeyUpdate | AnthropicKeyUpdate;

export class KeyPool {
  private keyProviders: KeyProvider[] = [];
  private recheckJobs: Partial<Record<LLMService, schedule.Job | null>> = {
    openai: null,
  };

  constructor() {
    this.keyProviders.push(new OpenAIKeyProvider());
    this.keyProviders.push(new AnthropicKeyProvider());
    this.keyProviders.push(new GoogleAIKeyProvider());
    this.keyProviders.push(new DeepSeekKeyProvider());
    this.keyProviders.push(new XAIKeyProvider());
    this.keyProviders.push(new GroqKeyProvider());
  }

  public init() {
    this.keyProviders.forEach((provider) => provider.init());
    const availableKeys = this.available("all");
    if (availableKeys === 0) {
      throw new Error("No keys loaded. Ensure that at least one key is configured.");
    }
    this.scheduleRecheck();
  }

  public get(model: string, service?: LLMService, multimodal?: boolean): Key {
    // hack for some claude requests needing keys with particular permissions
    // even though they use the same models as the non-multimodal requests
    if (multimodal) {
      model += "-multimodal";
    }

    const queryService = service || this.getServiceForModel(model);
    return this.getKeyProvider(queryService).get(model);
  }

  public list(): Omit<Key, "key">[] {
    return this.keyProviders.flatMap((provider) => provider.list());
  }

  /**
   * Marks a key as disabled for a specific reason. `revoked` should be used
   * to indicate a key that can never be used again, while `quota` should be
   * used to indicate a key that is still valid but has exceeded its quota.
   */
  public disable(key: Key, reason: "quota" | "revoked"): void {
    const service = this.getKeyProvider(key.service);
    service.disable(key);
    service.update(key.hash, { isRevoked: reason === "revoked" });
    if (service instanceof OpenAIKeyProvider || service instanceof AnthropicKeyProvider) {
      service.update(key.hash, { isOverQuota: reason === "quota" });
    }
  }

  public addKeys(service: LLMService, keys: string[]) {
    const keyProvider = this.getKeyProvider(service);
    let addedKeys = 0;

    for (const key of keys) {
      if (keyProvider.addKey({ key: key.trim(), isStartup: false })) addedKeys++;
    }

    return addedKeys;
  }

  public removeKeys(service: LLMService, hashes: string[]) {
    let removedKeys = 0;

    const keyProvider = this.getKeyProvider(service);

    for (const hash of hashes) {
      if (keyProvider.removeKey(hash)) removedKeys++;
    }

    return removedKeys;
  }

  public removeRevokedKeys(service: LLMService) {
    let removedKeys = 0;

    const keyProvider = this.getKeyProvider(service);
    const keys = keyProvider.list().filter((key) => key.isRevoked);

    for (const key of keys) {
      if (keyProvider.removeKey(key.hash)) removedKeys++;
    }

    return removedKeys;
  }

  public removeOverQuotaKeys(service: LLMService) {
    let removedKeys = 0;

    const keyProvider = this.getKeyProvider(service);
    const keys = keyProvider.list().filter((key) => {
      if ("isOverQuota" in key && typeof key.isOverQuota === "boolean") return key.isOverQuota;
      return false;
    });

    for (const key of keys) {
      if (keyProvider.removeKey(key.hash)) removedKeys++;
    }

    return removedKeys;
  }

  public update(key: Key, props: AllowedPartial): void {
    const service = this.getKeyProvider(key.service);
    service.update(key.hash, props);
  }

  public available(model: string | "all" = "all"): number {
    return this.keyProviders.reduce((sum, provider) => {
      const includeProvider =
        model === "all" || this.getServiceForModel(model) === provider.service;
      return sum + (includeProvider ? provider.available() : 0);
    }, 0);
  }

  public incrementUsage(key: Key, model: string, inputTokens: number, outputTokens: number): void {
    const provider = this.getKeyProvider(key.service);
    provider.incrementUsage(key.hash, model, inputTokens, outputTokens);
  }

  public getLockoutPeriod(family: ModelFamily): number {
    const service = MODEL_FAMILY_SERVICE[family];
    return this.getKeyProvider(service).getLockoutPeriod(family);
  }

  public markRateLimited(key: Key): void {
    const provider = this.getKeyProvider(key.service);
    provider.markRateLimited(key.hash);
  }

  public updateRateLimits(key: Key, headers: http.IncomingHttpHeaders): void {
    const provider = this.getKeyProvider(key.service);
    if (provider instanceof OpenAIKeyProvider) {
      provider.updateRateLimits(key.hash, headers);
    }
  }

  public recheck(service: LLMService): void {
    if (!config.checkKeys) {
      logger.info("Skipping key recheck because key checking is disabled");
      return;
    }

    const provider = this.getKeyProvider(service);
    provider.recheck();

    // Invalidate the model cache for the service
    void cacheStore.del(service);
  }

  private getServiceForModel(model: string): LLMService {
    if (model.includes("gpt") || model.startsWith("text-embedding-ada") || model.startsWith("o")) {
      return "openai";
    } else if (model.startsWith("claude-")) {
      return "anthropic";
    } else if (model.includes("gemini")) {
      return "google-ai";
    } else if (model.startsWith("deepseek")) {
      return "deepseek";
    } else if (model.startsWith("grok")) {
      return "xai";
    }
    throw new Error(`Unknown service for model '${model}'`);
  }

  public getKeyProvider(service: LLMService): KeyProvider {
    return this.keyProviders.find((provider) => provider.service === service)!;
  }

  /**
   * Schedules a periodic recheck of OpenAI keys, which runs every 8 hours on
   * a schedule offset by the server's hostname.
   */
  private scheduleRecheck(): void {
    const machineHash = crypto.createHash("sha256").update(os.hostname()).digest("hex");
    const offset = parseInt(machineHash, 16) % 7;
    const hour = [0, 8, 16].map((h) => h + offset).join(",");
    const crontab = `0 ${hour} * * *`;

    const job = schedule.scheduleJob(crontab, () => {
      const next = job.nextInvocation();
      logger.info({ next }, "Performing periodic recheck.");

      this.recheck("xai");
      this.recheck("openai");
      this.recheck("google-ai");
      this.recheck("deepseek");
      this.recheck("anthropic");
    });
    logger.info(
      { rule: crontab, next: job.nextInvocation() },
      "Scheduled periodic key recheck job"
    );
    this.recheckJobs.openai = job;
  }
}
