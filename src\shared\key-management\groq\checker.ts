import { AxiosError } from "axios";

import { config } from "@/config";

import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>roq<PERSON>eyProvider } from "./provider";

import { getAxiosInstance } from "../../network";
import { KeyCheckerBase } from "../key-checker-base";

const axios = getAxiosInstance();

const MIN_CHECK_INTERVAL = 3 * 1000; // 3 seconds
const KEY_CHECK_PERIOD = config.keys.groq.length > 0 ? 90 * 1000 : 0;

type GroqError = {
  error: {
    message: string;
    type: string;
    code: string;
  };
};

type GroqModelsResponse = {
  object: string;
  data: Array<{
    id: string;
    object: string;
    owned_by: string;
    active: boolean;
    context_window: number;
  }>;
};

type UpdateFn = typeof GroqKeyProvider.prototype.update;

export class GroqKeyChecker extends KeyCheckerBase<GroqKey> {
  constructor(keys: GroqK<PERSON>[], updateKey: UpdateFn) {
    super(keys, {
      service: "groq",
      keyCheckPeriod: KEY_CHECK_PERIOD,
      minCheckInterval: MIN_CHECK_INTERVAL,
      updateKey,
    });
  }

  protected async testKeyOrFail(key: GroqKey): Promise<void> {
    const opts = {
      headers: { Authorization: `Bearer ${key.key}`, "Content-Type": "application/json" },
    };

    const models = await axios.get<GroqModelsResponse>(
      "https://api.groq.com/openai/v1/models",
      opts
    );

    const allowedModelIds = [
      "deepseek-r1-distill-llama-70b",
      "moonshotai/kimi-k2-instruct",
      "qwen/qwen3-32b",
    ];
    const modelIds = models.data.data.map((m) => m.id).filter((m) => allowedModelIds.includes(m));
    const tier = await this.getKeyTier(key);

    this.updateKey(key.hash, { modelIds, tier });
    this.log.debug({ key: key.hash, modelIds, tier }, "Updated key models");
  }

  private async getKeyTier(key: GroqKey): Promise<GroqKey["tier"]> {
    const body = {
      max_tokens: 1,
      model: "llama-3.1-8b-instant",
      messages: [{ role: "user", content: "1+1=?" }],
    };

    try {
      const { headers } = await axios.post(
        "https://api.groq.com/openai/v1/chat/completions",
        body,
        { headers: { Authorization: `Bearer ${key.key}`, "Content-Type": "application/json" } }
      );

      return headers["x-ratelimit-limit-requests"] === "500000" ? "developer" : "free";
    } catch {
      return "free";
    }
  }

  protected async handleAxiosError(key: GroqKey, error: AxiosError): Promise<void> {
    if (error.response && GroqKeyChecker.errorIsGroqError(error)) {
      const { data } = error.response;

      if (
        data.error.type === "invalid_request_error" ||
        data.error.code === "organization_restricted"
      ) {
        this.log.warn(
          { key: key.hash, error: data },
          "Groq key is invalid or revoked. Disabling key."
        );
        this.updateKey(key.hash, { isDisabled: true, isRevoked: true });
        return;
      }
    } else {
      this.log.error(
        { key: key.hash, error: error.response?.data || error.message },
        "Error while checking Groq key"
      );
    }
  }

  static errorIsGroqError(error: AxiosError): error is AxiosError<GroqError> {
    const data = error.response?.data as GroqError;
    return !!data?.error.code;
  }
}
