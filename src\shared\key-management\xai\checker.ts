import { logger } from "@/logger";
import { KeyCheckerBase } from "../key-checker-base";
import type { XA<PERSON><PERSON><PERSON> } from "./provider";

import { AxiosError } from "axios";

import { getXAIModelFamily, type XAIModelFamily } from "../../models";
import { getAxiosInstance } from "../../network";

const log = logger.child({ module: "key-checker", service: "xai" });

const axios = getAxiosInstance();

const MIN_CHECK_INTERVAL = 3 * 1000; // 3 seconds
const KEY_CHECK_PERIOD = 60 * 60 * 1000; // 1 hour

type UpdateFn = (keyHash: string, update: Partial<XAIKey>) => void;

const POST_CHAT_COMPLETIONS_URL = "https://api.x.ai/v1/chat/completions";
const GET_MODELS_URL = "https://api.x.ai/v1/models";

type GetModelsResponse = {
  data: { id: string }[];
};

type XAIError = {
  error: string;
  code: string;
};

export class XAI<PERSON>eyChecker extends KeyCheckerBase<XAIKey> {
  constructor(keys: XAIKey[], updateKey: UpdateFn) {
    super(keys, {
      service: "xai",
      keyCheckPeriod: KEY_CHECK_PERIOD,
      minCheckInterval: MIN_CHECK_INTERVAL,
      recurringChecksEnabled: false,
      updateKey,
    });
  }

  protected async testKeyOrFail(key: XAIKey) {
    const isInitialCheck = !key.firstCheckedAt;

    if (isInitialCheck) {
      const [provisionedModels] = await Promise.all([
        this.getProvisionedModels(key),
        this.testLiveness(key),
      ]);
      this.updateKey(key.hash, { modelFamilies: provisionedModels, firstCheckedAt: Date.now() });
    } else {
      await this.testLiveness(key);
      this.updateKey(key.hash, {});
    }
    this.log.info({ key: key.hash, models: key.modelFamilies }, "Checked key.");
  }

  private async getProvisionedModels(key: XAIKey): Promise<XAIModelFamily[]> {
    const opts = { headers: XAIKeyChecker.getHeaders(key) };
    const { data } = await axios.get<GetModelsResponse>(GET_MODELS_URL, opts);
    const ids = new Set<string>();
    const families = new Set<XAIModelFamily>();

    data.data.forEach(({ id }) => {
      ids.add(id);
      families.add(getXAIModelFamily(id));
    });

    this.updateKey(key.hash, {
      modelIds: Array.from(ids),
      modelFamilies: Array.from(families),
    });

    return key.modelFamilies;
  }

  private async testLiveness(key: XAIKey): Promise<void> {
    const payload = {
      model: "grok-3-mini",
      max_completion_tokens: -1,
      messages: [{ role: "user", content: "" }],
    };
    await axios.post<XAIError>(POST_CHAT_COMPLETIONS_URL, payload, {
      headers: XAIKeyChecker.getHeaders(key),
      validateStatus: (status) => status === 400,
    });
  }

  protected handleAxiosError(key: XAIKey, error: AxiosError) {
    if (error.response && XAIKeyChecker.errorIsXAIError(error)) {
      const { status, data } = error.response;
      if (status === 400 && data.error?.includes("Incorrect API key")) {
        this.log.warn({ key: key.hash, error: data }, "Key is invalid. Disabling key.");
        this.updateKey(key.hash, { isDisabled: true, isRevoked: true });
      } else if (status === 403) {
        if (data.error?.includes("doesn't have any credits yet")) {
          this.log.warn(
            { key: key.hash, error: data },
            "Key doesn't have any credits. Disabling key."
          );
          this.updateKey(key.hash, { isDisabled: true, isOverQuota: true });
        }
      } else if (status === 429) {
        if (data.error?.includes("purchase more credits")) {
          this.log.warn({ key: key.hash, error: data }, "Key is over quota. Disabling key.");
          this.updateKey(key.hash, { isDisabled: true, isOverQuota: true });
        } else {
          this.log.warn(
            { key: key.hash, error: data },
            "Key has a non-transient 429 error. Disabling key."
          );
          this.updateKey(key.hash, { isDisabled: true, isOverQuota: false });
        }
      } else {
        this.log.error(
          { key: key.hash, status, error: data },
          "Encountered unexpected error status while checking key."
        );
        this.updateKey(key.hash, { lastCheckedAt: Date.now() });
      }

      return;
    }

    this.log.error(
      { key: key.hash, error: error.message },
      "Network error while checking key; trying this key again in a minute."
    );
    const oneMinute = 60 * 1000;
    const next = Date.now() - (KEY_CHECK_PERIOD - oneMinute);
    this.updateKey(key.hash, { lastCheckedAt: next });
  }

  static errorIsXAIError(error: AxiosError): error is AxiosError<XAIError> {
    const data = error.response?.data as any;
    return data?.code;
  }

  static getHeaders(key: XAIKey) {
    return { Authorization: `Bearer ${key.key}` };
  }
}
