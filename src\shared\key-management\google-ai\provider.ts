import crypto from "crypto";

import { config } from "@/config";
import { logger } from "@/logger";

import { PaymentRequiredError } from "@/shared/errors";
import { getGoogleAIModelFamily, type GoogleAIModelFamily } from "@/shared/models";

import { getTokenCostUsd } from "@/shared/stats";
import {
  createGenericGetLockoutPeriod,
  defaultUsages,
  type Key,
  type KeyProvider,
  type Usages,
} from "..";
import { prioritizeKeys } from "../prioritize-keys";
import { GoogleAIKeyChecker } from "./checker";

// Note that Google AI is not the same as Vertex AI, both are provided by
// Google but Vertex is the GCP product for enterprise, while Google API is a
// development/hobbyist product. They use completely different APIs and keys.
// https://ai.google.dev/docs/migrate_to_cloud

export type GoogleAIKeyUpdate = Omit<
  Partial<GoogleAIKey>,
  "key" | "hash" | "lastUsedAt" | "prompts" | "rateLimitedAt" | "rateLimitedUntil"
>;

type GoogleAIKeyUsage = {
  [K in GoogleAIModelFamily as `${K}-usages`]: Usages;
};

export interface GoogleAIKey extends Key, GoogleAIKeyUsage {
  readonly service: "google-ai";
  readonly modelFamilies: GoogleAIModelFamily[];
  /** All detected model IDs on this key. */
  modelIds: string[];

  /** Tier of the key. */
  tier: "free" | "paid";
}

/**
 * Upon being rate limited, a key will be locked out for this many milliseconds
 * while we wait for other concurrent requests to finish.
 */
const RATE_LIMIT_LOCKOUT = 2000;
/**
 * Upon assigning a key, we will wait this many milliseconds before allowing it
 * to be used again. This is to prevent the queue from flooding a key with too
 * many requests while we wait to learn whether previous ones succeeded.
 */
const KEY_REUSE_DELAY = 500;

export class GoogleAIKeyProvider implements KeyProvider<GoogleAIKey> {
  readonly service = "google-ai";

  private keys: GoogleAIKey[] = [];
  private checker?: GoogleAIKeyChecker;
  private log = logger.child({ module: "key-provider", service: this.service });

  constructor() {
    const keyConfig = config.keys.google;

    if (keyConfig.length === 0) {
      this.log.warn("GOOGLE_AI_KEYS is not set. Google AI API will not be available.");
      return;
    }

    for (const key of keyConfig) this.addKey({ key, isStartup: true });
    this.log.info({ keyCount: this.keys.length }, "Loaded Google AI keys.");
  }

  addKey({ key, isStartup = false }: { key: string; isStartup?: boolean }): boolean {
    const hash = `plm-${crypto.createHash("sha256").update(key).digest("hex").slice(0, 8)}`;

    if (!isStartup) {
      const isExist = this.keys.find((k) => k.hash === hash);
      if (isExist) return false;
    }

    const newKey: GoogleAIKey = {
      key,
      service: this.service,
      modelFamilies: ["gemini-flash-lite", "gemini-flash", "gemini-pro"],
      isDisabled: false,
      isRevoked: false,
      tier: "free",
      input: { tokens: 0, cost: 0 },
      output: { tokens: 0, cost: 0 },
      prompts: 0,
      lastUsedAt: 0,
      rateLimitedAt: 0,
      rateLimitedUntil: 0,
      hash: hash,
      lastCheckedAt: 0,
      firstCheckedAt: 0,
      addedAt: Date.now(),
      modelIds: [],

      "gemini-flash-lite-usages": structuredClone(defaultUsages),
      "gemini-flash-usages": structuredClone(defaultUsages),
      "gemini-pro-usages": structuredClone(defaultUsages),
    };
    this.keys.push(newKey);
    return true;
  }

  removeKey(hash: string): boolean {
    const keyIndex = this.keys.findIndex((k) => k.hash === hash);
    if (keyIndex === -1) return false;

    this.keys.splice(keyIndex, 1);
    return true;
  }

  public init() {
    if (config.checkKeys) {
      this.checker = new GoogleAIKeyChecker(this.keys, this.update.bind(this));
      this.checker.start();
    }
  }

  public list() {
    return this.keys.map((k) => Object.freeze({ ...k, key: undefined }));
  }

  private getKey(model: string) {
    const neededFamily = getGoogleAIModelFamily(model);
    const availableKeys = this.keys.filter(
      (k) => !k.isDisabled && !k.isRevoked && k.modelFamilies.includes(neededFamily)
    );

    switch (true) {
      default:
      case config.checkKeys === false:
        return prioritizeKeys(availableKeys);

      // 2.5-pro-exp currently disabled temporarily
      // case model.includes("2.5-pro-exp"):
      //   return prioritizeKeys(availableKeys.filter((k) => k.tier === "free"));

      // Handle every model that have been redirected to 2.5-pro
      case model.includes("2.5-pro"):
      case model.includes("2.0-pro-exp"):
      case model.includes("1.5-pro-exp"):
      case !!model.match(/exp-\d{4}/):
        return prioritizeKeys(availableKeys.filter((k) => k.tier === "paid"));
    }
  }

  public get(model: string) {
    const selectedKey = this.getKey(model)[0];

    selectedKey.lastUsedAt = Date.now();
    this.throttle(selectedKey.hash);
    return { ...selectedKey };
  }

  public disable(key: GoogleAIKey) {
    const keyFromPool = this.keys.find((k) => k.hash === key.hash);
    if (!keyFromPool || keyFromPool.isDisabled) return;
    keyFromPool.isDisabled = true;
    this.log.warn({ key: key.hash }, "Key disabled");
  }

  public update(hash: string, update: Partial<GoogleAIKey>) {
    const keyFromPool = this.keys.find((k) => k.hash === hash)!;
    Object.assign(keyFromPool, { lastCheckedAt: Date.now(), ...update });
  }

  public available() {
    return this.keys.filter((k) => !k.isDisabled).length;
  }

  public incrementUsage(keyHash: string, model: string, inputTokens: number, outputTokens: number) {
    const key = this.keys.find((k) => k.hash === keyHash);
    if (!key) return;

    const modelFamily = getGoogleAIModelFamily(model);
    const cost = getTokenCostUsd({ modelFamily, inputTokens, outputTokens });

    key.prompts++;
    key.input.tokens += inputTokens;
    key.output.tokens += outputTokens;
    key.input.cost += cost.input;
    key.output.cost += cost.output;

    key[`${modelFamily}-usages`].prompts += 1;
    key[`${modelFamily}-usages`].input.tokens += inputTokens;
    key[`${modelFamily}-usages`].output.tokens += outputTokens;
    key[`${modelFamily}-usages`].input.cost += cost.input;
    key[`${modelFamily}-usages`].output.cost += cost.output;
  }

  getLockoutPeriod = createGenericGetLockoutPeriod(() => this.keys);

  /**
   * This is called when we receive a 429, which means there are already five
   * concurrent requests running on this key. We don't have any information on
   * when these requests will resolve, so all we can do is wait a bit and try
   * again. We will lock the key for 2 seconds after getting a 429 before
   * retrying in order to give the other requests a chance to finish.
   */
  public markRateLimited(keyHash: string) {
    this.log.debug({ key: keyHash }, "Key rate limited");
    const key = this.keys.find((k) => k.hash === keyHash)!;
    const now = Date.now();
    key.rateLimitedAt = now;
    key.rateLimitedUntil = now + RATE_LIMIT_LOCKOUT;
  }

  public recheck() {
    this.keys.forEach(({ hash }) => this.update(hash, { lastCheckedAt: 0, isDisabled: false }));
    this.checker?.scheduleNextCheck();
  }

  /**
   * Applies a short artificial delay to the key upon dequeueing, in order to
   * prevent it from being immediately assigned to another request before the
   * current one can be dispatched.
   **/
  private throttle(hash: string) {
    const now = Date.now();
    const key = this.keys.find((k) => k.hash === hash)!;

    const currentRateLimit = key.rateLimitedUntil;
    const nextRateLimit = now + KEY_REUSE_DELAY;

    key.rateLimitedAt = now;
    key.rateLimitedUntil = Math.max(currentRateLimit, nextRateLimit);
  }
}
