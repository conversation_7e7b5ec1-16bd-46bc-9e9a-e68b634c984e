import crypto from "crypto";

import { config } from "@/config";
import { logger } from "@/logger";

import { PaymentRequiredError } from "@/shared/errors";
import { getGroqModelFamily, type GroqModelFamily } from "@/shared/models";

import { getTokenCostUsd } from "@/shared/stats";
import {
  createGenericGetLockoutPeriod,
  defaultUsages,
  type Key,
  type KeyProvider,
  type Usages,
} from "../index";
import { prioritizeKeys } from "../prioritize-keys";
import { GroqKeyChecker } from "./checker";

type GroqKeyUsage = {
  [K in GroqModelFamily as `${K}-usages`]: Usages;
};

export interface GroqKey extends Key, GroqKeyUsage {
  readonly service: "groq";
  readonly modelFamilies: GroqModelFamily[];

  /** Set when key check returns a non-transient 429. */
  isOverQuota: boolean;
  /**
   * Model snapshots available.
   */
  modelIds: string[];
  /**
   * Tier of the key.
   */
  tier: "free" | "developer";
}

/**
 * Upon being rate limited, a key will be locked out for this many milliseconds
 * while we wait for other concurrent requests to finish.
 */
const RATE_LIMIT_LOCKOUT = 2000;
/**
 * Upon assigning a key, we will wait this many milliseconds before allowing it
 * to be used again. This is to prevent the queue from flooding a key with too
 * many requests while we wait to learn whether previous ones succeeded.
 */
const KEY_REUSE_DELAY = 500;

export class GroqKeyProvider implements KeyProvider<GroqKey> {
  readonly service = "groq";
  private keys: GroqKey[] = [];
  private checker?: GroqKeyChecker;
  private log = logger.child({ module: "key-provider", service: this.service });

  constructor() {
    const keyConfig = config.keys.groq;

    if (keyConfig.length === 0) {
      this.log.warn("GROQ_KEYS is not set. Groq API will not be available.");
      return;
    }

    for (const key of keyConfig) this.addKey({ key, isStartup: true });
    this.log.info({ keyCount: this.keys.length }, "Loaded Groq keys.");
  }

  addKey({ key, isStartup = false }: { key: string; isStartup?: boolean }): boolean {
    const hash = `groq-${crypto.createHash("sha256").update(key).digest("hex").slice(0, 8)}`;

    if (!isStartup) {
      const isExist = this.keys.find((k) => k.hash === hash);
      if (isExist) return false;
    }

    const newKey: GroqKey = {
      key,
      hash: hash,
      service: this.service,
      modelFamilies: ["deepseek-r1-distill", "kimi-k2", "qwen3"],
      isDisabled: false,
      isOverQuota: false,
      isRevoked: false,
      tier: "free",
      input: { tokens: 0, cost: 0 },
      output: { tokens: 0, cost: 0 },
      prompts: 0,
      lastUsedAt: 0,
      rateLimitedAt: 0,
      rateLimitedUntil: 0,
      lastCheckedAt: 0,
      firstCheckedAt: 0,
      addedAt: Date.now(),
      modelIds: [],

      "qwen3-usages": structuredClone(defaultUsages),
      "kimi-k2-usages": structuredClone(defaultUsages),
      "deepseek-r1-distill-usages": structuredClone(defaultUsages),
    };

    this.keys.push(newKey);
    return true;
  }

  public init() {
    if (config.checkKeys) {
      this.checker = new GroqKeyChecker(this.keys, this.update.bind(this));
      this.checker.start();
    }
  }

  public list(): Omit<GroqKey, "key">[] {
    return this.keys.map((k) => ({ ...k, key: undefined! }));
  }

  public get(model: string): GroqKey {
    const availableKeys = this.keys.filter((k) => !k.isDisabled);
    if (availableKeys.length === 0) {
      let message = "No Groq keys available.";
      if (this.keys.length === 0) {
        message = "No Groq keys configured.";
      }
      throw new PaymentRequiredError(message);
    }

    const requestedFamily = getGroqModelFamily(model);
    const compatibleKeys = availableKeys.filter((k) => k.modelFamilies.includes(requestedFamily));

    if (compatibleKeys.length === 0) {
      throw new PaymentRequiredError(
        `No Groq keys available for model family '${requestedFamily}'.`
      );
    }

    const now = Date.now();

    const eligibleKeys = compatibleKeys.filter((key) => {
      const timeSinceLastUsed = now - key.lastUsedAt;
      const isReusable = timeSinceLastUsed >= KEY_REUSE_DELAY;
      const isNotRateLimited = now >= key.rateLimitedUntil;
      return isReusable && isNotRateLimited;
    });

    if (eligibleKeys.length === 0) {
      const rateLimitedKeys = compatibleKeys.filter((key) => now < key.rateLimitedUntil);
      if (rateLimitedKeys.length > 0) {
        const nextAvailable = Math.min(...rateLimitedKeys.map((key) => key.rateLimitedUntil));
        const delay = nextAvailable - now;
        throw new PaymentRequiredError(
          `All Groq keys are rate limited. Try again in ${Math.ceil(delay / 1000)} seconds.`
        );
      } else {
        throw new PaymentRequiredError(
          "All Groq keys are in use. Please try again in a few seconds."
        );
      }
    }

    // Prioritize developer tier keys first, then fall back to free tier
    const developerKeys = eligibleKeys.filter((key) => key.tier === "developer");
    const freeKeys = eligibleKeys.filter((key) => key.tier === "free");

    const prioritizedKeys = developerKeys.length > 0 ? developerKeys : freeKeys;
    const selectedKey = prioritizeKeys(prioritizedKeys)[0];

    selectedKey.lastUsedAt = now;
    this.throttle(selectedKey.hash);
    return selectedKey;
  }

  public disable(key: GroqKey): void {
    const keyFromPool = this.keys.find((k) => k.hash === key.hash);
    if (!keyFromPool || keyFromPool.isDisabled) return;
    keyFromPool.isDisabled = true;
    this.log.warn({ key: key.hash }, "Key disabled");
  }

  public update(keyHash: string, update: Partial<GroqKey>) {
    const keyFromPool = this.keys.find((k) => k.hash === keyHash)!;
    Object.assign(keyFromPool, { lastCheckedAt: Date.now(), ...update });
  }

  public available(): number {
    return this.keys.filter((k) => !k.isDisabled).length;
  }

  public incrementUsage(
    hash: string,
    model: string,
    inputTokens: number,
    outputTokens: number
  ): void {
    const key = this.keys.find((k) => k.hash === hash);
    if (!key) return;

    key.input.tokens += inputTokens;
    key.output.tokens += outputTokens;
    key.prompts++;

    const modelFamily = getGroqModelFamily(model);
    const cost = getTokenCostUsd({ modelFamily, inputTokens, outputTokens });
    key.input.cost += cost.input;
    key.output.cost += cost.output;

    const family = getGroqModelFamily(model);
    const familyUsage = key[`${family}-usages`] as Usages;
    familyUsage.input.tokens += inputTokens;
    familyUsage.output.tokens += outputTokens;
    familyUsage.prompts++;
    familyUsage.input.cost += cost.input;
    familyUsage.output.cost += cost.output;
  }

  public getLockoutPeriod = createGenericGetLockoutPeriod(() => this.keys);

  public markRateLimited(hash: string): void {
    this.log.debug({ key: hash }, "Key rate limited");
    const key = this.keys.find((k) => k.hash === hash)!;
    const now = Date.now();
    key.rateLimitedAt = now;
    key.rateLimitedUntil = now + RATE_LIMIT_LOCKOUT;
  }

  public recheck(): void {
    this.keys.forEach((key) => {
      this.update(key.hash, {
        isOverQuota: false,
        isDisabled: false,
        lastCheckedAt: 0,
      });
    });
    this.checker?.scheduleNextCheck();
  }

  public removeKey(hash: string): boolean {
    const keyIndex = this.keys.findIndex((k) => k.hash === hash);
    if (keyIndex === -1) return false;
    this.keys.splice(keyIndex, 1);
    return true;
  }

  private throttle(hash: string): void {
    const key = this.keys.find((k) => k.hash === hash)!;

    const now = Date.now();
    const timeSinceLastUsed = now - key.lastUsedAt;
    const minDelay = KEY_REUSE_DELAY;

    if (timeSinceLastUsed < minDelay) {
      const delayUntil = key.lastUsedAt + minDelay;
      key.rateLimitedUntil = Math.max(key.rateLimitedUntil, delayUntil);
    }
  }
}
