import { RedisStore } from "connect-redis";
import cookieParser from "cookie-parser";
import expressSession from "express-session";
import MemoryS<PERSON> from "memorystore";
import { createClient } from "redis";

import { logger as baseLogger } from "@/logger";
import { config, SECRET_SIGNING_KEY } from "../config";

const ONE_WEEK = 1000 * 60 * 60 * 24 * 7;
const logger = baseLogger.child({ module: "redis" });

export const redisClient = config.redisUrl
  ? await createClient({ url: config.redisUrl })
      .on("error", (err) => logger.error(err, "Redis error"))
      .connect()
  : undefined;

const store = redisClient
  ? new RedisStore({ client: redisClient, prefix: `${config.enviroment}:session:` })
  : new (MemoryStore(expressSession))({ checkPeriod: ONE_WEEK });

const cookieParserMiddleware = cookieParser(SECRET_SIGNING_KEY);
const sessionMiddleware = expressSession({
  secret: SECRET_SIGNING_KEY,
  resave: false,
  saveUninitialized: false,
  store,
  cookie: {
    sameSite: "strict",
    maxAge: ONE_WEEK,
    signed: true,
    secure: !config.useInsecureCookies,
  },
});

const withSession = [cookieParserMiddleware, sessionMiddleware];

export { withSession };
