import crypto from "crypto";

import { config } from "@/config";
import { logger } from "@/logger";

import { PaymentRequiredError } from "@/shared/errors";
import { getDeepSeekModelFamily, type DeepSeekModelFamily } from "@/shared/models";

import { getTokenCostUsd } from "@/shared/stats";
import {
  createGenericGetLockoutPeriod,
  defaultUsages,
  type Key,
  type KeyProvider,
  type Usages,
} from "../index";
import { prioritizeKeys } from "../prioritize-keys";
import { DeepSeekKeyChecker } from "./checker";

type DeepSeekKeyUsage = {
  [K in DeepSeekModelFamily as `${K}-usages`]: Usages;
};

export interface DeepSeekKey extends Key, DeepSeekKeyUsage {
  readonly service: "deepseek";
  readonly modelFamilies: DeepSeekModelFamily[];

  /** Set when key check returns a non-transient 429. */
  isOverQuota: boolean;
  /**
   * Model snapshots available.
   */
  modelIds: string[];
}

/**
 * Upon being rate limited, a key will be locked out for this many milliseconds
 * while we wait for other concurrent requests to finish.
 */
const RATE_LIMIT_LOCKOUT = 2000;
/**
 * Upon assigning a key, we will wait this many milliseconds before allowing it
 * to be used again. This is to prevent the queue from flooding a key with too
 * many requests while we wait to learn whether previous ones succeeded.
 */
const KEY_REUSE_DELAY = 500;

export class DeepSeekKeyProvider implements KeyProvider<DeepSeekKey> {
  readonly service = "deepseek";
  private keys: DeepSeekKey[] = [];
  private checker?: DeepSeekKeyChecker;
  private log = logger.child({ module: "key-provider", service: this.service });

  constructor() {
    const keyConfig = config.keys.deepseek;

    if (keyConfig.length === 0) {
      this.log.warn("DEEPSEEK_KEYS is not set. DeepSeek API will not be available.");
      return;
    }

    for (const key of keyConfig) this.addKey({ key, isStartup: true });
    this.log.info({ keyCount: this.keys.length }, "Loaded DeepSeek keys.");
  }

  addKey({ key, isStartup = false }: { key: string; isStartup?: boolean }): boolean {
    const hash = `dps-${crypto.createHash("sha256").update(key).digest("hex").slice(0, 8)}`;

    if (!isStartup) {
      const isExist = this.keys.find((k) => k.hash === hash);
      if (isExist) return false;
    }

    const newKey: DeepSeekKey = {
      key,
      hash: hash,
      service: this.service,
      modelFamilies: ["deepseek-chat", "deepseek-reasoner"],
      isDisabled: false,
      isOverQuota: false,
      isRevoked: false,
      input: { tokens: 0, cost: 0 },
      output: { tokens: 0, cost: 0 },
      prompts: 0,
      lastUsedAt: 0,
      rateLimitedAt: 0,
      rateLimitedUntil: 0,
      lastCheckedAt: 0,
      firstCheckedAt: 0,
      addedAt: Date.now(),
      modelIds: [],

      "deepseek-chat-usages": structuredClone(defaultUsages),
      "deepseek-reasoner-usages": structuredClone(defaultUsages),
    };
    this.keys.push(newKey);
    return true;
  }

  removeKey(hash: string): boolean {
    const keyIndex = this.keys.findIndex((k) => k.hash === hash);
    if (keyIndex === -1) return false;

    this.keys.splice(keyIndex, 1);
    return true;
  }

  public init() {
    if (config.checkKeys) {
      this.checker = new DeepSeekKeyChecker(this.keys, this.update.bind(this));
      this.checker.start();
    }
  }

  public list() {
    return this.keys.map((k) => Object.freeze({ ...k, key: undefined }));
  }

  public get(rawModel: string) {
    this.log.debug({ model: rawModel }, "Selecting key");

    const availableKeys = this.keys.filter((k) => !k.isDisabled && !k.isRevoked);

    if (availableKeys.length === 0) {
      throw new PaymentRequiredError("No DeepSeek keys available.");
    }

    const keysByPriority = prioritizeKeys(availableKeys);

    const selectedKey = keysByPriority[0];
    selectedKey.lastUsedAt = Date.now();
    this.throttle(selectedKey.hash);
    return { ...selectedKey };
  }

  public disable(key: DeepSeekKey) {
    const keyFromPool = this.keys.find((k) => k.hash === key.hash);
    if (!keyFromPool || keyFromPool.isDisabled) return;
    keyFromPool.isDisabled = true;
    this.log.warn({ key: key.hash }, "Key disabled");
  }

  public update(hash: string, update: Partial<DeepSeekKey>) {
    const keyFromPool = this.keys.find((k) => k.hash === hash)!;
    Object.assign(keyFromPool, { lastCheckedAt: Date.now(), ...update });
  }

  public available() {
    return this.keys.filter((k) => !k.isDisabled && !k.isOverQuota).length;
  }

  public incrementUsage(keyHash: string, model: string, inputTokens: number, outputTokens: number) {
    const key = this.keys.find((k) => k.hash === keyHash);
    if (!key) return;

    const modelFamily = getDeepSeekModelFamily(model);
    const cost = getTokenCostUsd({ modelFamily, inputTokens, outputTokens });

    key.prompts++;
    key.input.tokens += inputTokens;
    key.output.tokens += outputTokens;
    key.input.cost += cost.input;
    key.output.cost += cost.output;

    key[`${modelFamily}-usages`].prompts += 1;
    key[`${modelFamily}-usages`].input.tokens += inputTokens;
    key[`${modelFamily}-usages`].output.tokens += outputTokens;
    key[`${modelFamily}-usages`].input.cost += cost.input;
    key[`${modelFamily}-usages`].output.cost += cost.output;
  }

  getLockoutPeriod = createGenericGetLockoutPeriod(() => this.keys);

  /**
   * This is called when we receive a 429, which means there are already five
   * concurrent requests running on this key. We don't have any information on
   * when these requests will resolve, so all we can do is wait a bit and try
   * again. We will lock the key for 2 seconds after getting a 429 before
   * retrying in order to give the other requests a chance to finish.
   */
  public markRateLimited(keyHash: string) {
    this.log.debug({ key: keyHash }, "Key rate limited");
    const key = this.keys.find((k) => k.hash === keyHash)!;
    const now = Date.now();
    key.rateLimitedAt = now;
    key.rateLimitedUntil = now + RATE_LIMIT_LOCKOUT;
  }

  public recheck() {
    this.keys.forEach((key) => {
      this.update(key.hash, {
        isOverQuota: false,
        isDisabled: false,
        lastCheckedAt: 0,
      });
    });
    this.checker?.scheduleNextCheck();
  }

  /**
   * Applies a short artificial delay to the key upon dequeueing, in order to
   * prevent it from being immediately assigned to another request before the
   * current one can be dispatched.
   **/
  private throttle(hash: string) {
    const now = Date.now();
    const key = this.keys.find((k) => k.hash === hash)!;

    const currentRateLimit = key.rateLimitedUntil;
    const nextRateLimit = now + KEY_REUSE_DELAY;

    key.rateLimitedAt = now;
    key.rateLimitedUntil = Math.max(currentRateLimit, nextRateLimit);
  }
}
