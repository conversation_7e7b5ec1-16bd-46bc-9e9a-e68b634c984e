import { type Request, type <PERSON><PERSON><PERSON><PERSON><PERSON>, Router } from "express";
import { v4 } from "uuid";

import { cacheStore } from "@/shared/cache";
import { type Google<PERSON><PERSON><PERSON><PERSON>, keyPool } from "@/shared/key-management";
import { isAllowedModel } from "@/shared/models";
import { createModelList } from "@/shared/utils";

import {
  addKey,
  createPreprocessorMiddleware,
  finalizeBody,
  finalizeSignedRequest,
} from "../middleware/request";
import { addGoogleAIKey } from "../middleware/request/mutators/add-google-ai-key";
import { createQueuedProxyMiddleware } from "../middleware/request/proxy-middleware-factory";
import type { ProxyResHandlerWithBody } from "../middleware/response";
import { ipLimiter } from "../rate-limit";

export function getModelsResponse() {
  const provider = keyPool.getKeyProvider("google-ai");
  if (provider.available() === 0) return [];

  const keys = provider.list() as GoogleAIKey[];
  const modelIds = Array.from(new Set(keys.map((k) => k.modelIds).flat()));

  return createModelList(
    modelIds,
    "google-ai",
    (id) => isAllowedModel(id) && id.startsWith("models/gemini")
  );
}

const handleModelRequest: RequestHandler = async (_req, res) => {
  const cache = await cacheStore.get<ReturnType<typeof getModelsResponse>>("google-ai");

  if (cache) {
    res.setHeader("Cache-State", "HIT");
    return res.status(200).json({ object: "list", data: cache });
  }

  const models = getModelsResponse();
  await cacheStore.set("google-ai", models);

  res.setHeader("Cache-State", "MISS");
  return res.status(200).json({ object: "list", data: models });
};

const googleAIBlockingResponseHandler: ProxyResHandlerWithBody = async (
  _proxyRes,
  req,
  res,
  body
) => {
  if (typeof body !== "object") {
    throw new Error("Expected body to be an object");
  }

  let newBody = body;
  if (req.inboundApi === "openai") {
    req.log.info("Transforming Google AI response to OpenAI format");
    newBody = transformGoogleAIResponse(body, req);
  }

  res.status(200).json({ ...newBody, proxy: body.proxy });
};

const openaiResponseHandler: ProxyResHandlerWithBody = async (_proxyRes, req, res, body) => {
  if (typeof body !== "object") {
    throw new Error("Expected body to be an object");
  }

  let newBody = body;
  res.status(200).json({ ...newBody, proxy: body.proxy });
};

type GoogleAIResponse = {
  candidates: {
    index: number;
    tokenCount: number;
    finishReason: string;
    safetyRatings: { category: string; probability: string }[];
    content: { parts: { text: string; thought?: boolean }[]; role: string };
  }[];
  modelVersion: string;
  usageMetadata: {
    totalTokenCount: number;
    promptTokenCount: number;
    thoughtsTokenCount: number;
    candidatesTokenCount: number;
    promptTokensDetails: { modality: string; tokenCount: number }[];
  };
};

function transformGoogleAIResponse(
  rawBody: Record<string, unknown>,
  req: Request
): Record<string, any> {
  const body = rawBody as GoogleAIResponse;

  const total_tokens = (req.promptTokens ?? 0) + (req.outputTokens ?? 0);
  const parts = body.candidates?.[0]?.content?.parts ?? [{ text: "" }];
  const content = parts
    .reduce(
      (acc, { text, thought }) =>
        acc + (thought ? `<thinking>\n${text.trim()}\n</thinking>\n` : text + "\n"),
      ""
    )
    .trim();

  const model = body.modelVersion.replace("models/", "") || req.body.model;
  const completion_tokens_details = req.reasoningTokens
    ? { reasoning_tokens: req.reasoningTokens }
    : undefined;

  return {
    id: "goo-" + v4(),
    object: "chat.completion",
    created: Date.now(),
    model,
    usage: {
      prompt_tokens: req.promptTokens,
      completion_tokens: req.outputTokens,
      total_tokens,
      completion_tokens_details,
    },
    choices: [
      {
        message: { role: "assistant", content },
        finish_reason: body.candidates[0].finishReason,
        index: 0,
      },
    ],
  };
}

const googleAIProxy = createQueuedProxyMiddleware({
  target: ({ signedRequest }) => {
    if (!signedRequest) throw new Error("Must sign request before proxying");
    const { protocol, hostname } = signedRequest;
    return `${protocol}//${hostname}`;
  },
  mutations: [addGoogleAIKey, finalizeSignedRequest],
  blockingResponseHandler: googleAIBlockingResponseHandler,
});

const openAIToGoogleAIProxy = createQueuedProxyMiddleware({
  mutations: [addKey, finalizeBody],
  target: "https://generativelanguage.googleapis.com/",
  blockingResponseHandler: openaiResponseHandler,
});

const googleAIRouter = Router();
googleAIRouter.get("/v1/models", handleModelRequest);
googleAIRouter.get("/v1beta/models", handleModelRequest);
googleAIRouter.get(["/v1beta/openai/models", "/v1beta/openai/v1/models"], handleModelRequest);

googleAIRouter.post(
  ["/v1beta/openai/chat/completions", "/v1beta/openai/v1/chat/completions"],
  ipLimiter,
  createPreprocessorMiddleware(
    { inboundApi: "openai", outboundApi: "openai", service: "google-ai" },
    { afterTransform: [maybeReassignModel, removePenalty] }
  ),
  openAIToGoogleAIProxy
);

// Native Google AI chat completion endpoint
googleAIRouter.post(
  "/:apiVersion(v1alpha|v1beta)/models/:modelId:(generateContent|streamGenerateContent)",
  ipLimiter,
  createPreprocessorMiddleware(
    { inboundApi: "google-ai", outboundApi: "google-ai", service: "google-ai" },
    { beforeTransform: [maybeReassignModel], afterTransform: [setStreamFlag] }
  ),
  googleAIProxy
);

// OpenAI-to-Google AI compatibility endpoint.
googleAIRouter.post(
  "/v1/chat/completions",
  ipLimiter,
  createPreprocessorMiddleware(
    { inboundApi: "openai", outboundApi: "google-ai", service: "google-ai" },
    { afterTransform: [maybeReassignModel] }
  ),
  googleAIProxy
);

function setStreamFlag(req: Request) {
  const isStreaming = req.url.includes("streamGenerateContent");
  if (isStreaming) {
    req.body.stream = true;
    req.isStreaming = true;
  } else {
    req.body.stream = false;
    req.isStreaming = false;
  }
}

function removePenalty(req: Request) {
  if (req.body.model.includes("2.5-pro")) {
    delete req.body.presence_penalty;
    delete req.body.frequency_penalty;
  }
}

/**
 * Replaces requests for non-Google AI models with gemini-2.0-flash-lite.
 *
 * Also strips `models/` from the beginning of the model IDs.
 **/
function maybeReassignModel(req: Request) {
  // Ensure model is on body as a lot of middleware will expect it.
  const model = req.body.model || req.url.split("/").pop()?.split(":").shift();
  if (!model) {
    throw new Error("You must specify a model with your request.");
  }
  req.body.model = model;

  const requested = model;
  if (requested.startsWith("models/")) {
    req.body.model = requested.slice("models/".length);
  }

  if (requested.includes("gemini")) {
    return;
  }

  req.log.info({ requested }, "Reassigning model to gemini-2.0-flash-lite");
  req.body.model = "gemini-2.0-flash-lite";
}

export const googleAI = googleAIRouter;
